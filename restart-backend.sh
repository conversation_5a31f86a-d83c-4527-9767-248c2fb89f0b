#!/bin/bash

# 重启后端服务脚本
# 适用于CentOS 7云服务器

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}        重启PMS后端服务                ${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 项目路径
PROJECT_PATH="/opt/pms/product-management-system"
JAR_FILE="target/product-management-system-1.0-SNAPSHOT.jar"
LOG_FILE="logs/app.log"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo -e "${RED}请使用sudo运行此脚本${NC}"
    exit 1
fi

# 1. 停止现有服务
echo -e "${YELLOW}1. 停止现有后端服务...${NC}"

# 方法1：尝试使用systemctl停止
if systemctl is-active --quiet pms-backend; then
    echo "使用systemctl停止服务..."
    systemctl stop pms-backend
    sleep 3
    
    if systemctl is-active --quiet pms-backend; then
        echo -e "${RED}systemctl停止失败，尝试手动停止${NC}"
    else
        echo -e "${GREEN}✅ 服务已通过systemctl停止${NC}"
    fi
fi

# 方法2：手动查找并停止Java进程
JAVA_PID=$(ps aux | grep "product-management-system" | grep -v grep | awk '{print $2}')
if [ ! -z "$JAVA_PID" ]; then
    echo "发现Java进程 PID: $JAVA_PID"
    echo "正在停止进程..."
    kill -15 $JAVA_PID
    sleep 5
    
    # 检查进程是否还在运行
    if ps -p $JAVA_PID > /dev/null; then
        echo "进程仍在运行，强制停止..."
        kill -9 $JAVA_PID
        sleep 2
    fi
    
    echo -e "${GREEN}✅ Java进程已停止${NC}"
else
    echo "未发现运行中的Java进程"
fi

# 2. 检查项目目录
echo -e "${YELLOW}2. 检查项目目录...${NC}"
if [ ! -d "$PROJECT_PATH" ]; then
    echo -e "${RED}❌ 项目目录不存在: $PROJECT_PATH${NC}"
    exit 1
fi

cd $PROJECT_PATH
echo "当前目录: $(pwd)"

# 3. 检查JAR文件
echo -e "${YELLOW}3. 检查JAR文件...${NC}"
if [ ! -f "$JAR_FILE" ]; then
    echo -e "${RED}❌ JAR文件不存在: $JAR_FILE${NC}"
    echo "请先构建项目: mvn clean package -DskipTests"
    exit 1
fi

echo -e "${GREEN}✅ JAR文件存在${NC}"

# 4. 创建日志目录
echo -e "${YELLOW}4. 准备日志目录...${NC}"
mkdir -p logs
echo -e "${GREEN}✅ 日志目录已准备${NC}"

# 5. 启动服务
echo -e "${YELLOW}5. 启动后端服务...${NC}"

# 方法1：尝试使用systemctl启动
if systemctl list-unit-files | grep -q pms-backend; then
    echo "使用systemctl启动服务..."
    systemctl start pms-backend
    sleep 5
    
    if systemctl is-active --quiet pms-backend; then
        echo -e "${GREEN}✅ 服务已通过systemctl启动${NC}"
        systemctl status pms-backend
    else
        echo -e "${RED}systemctl启动失败，尝试手动启动${NC}"
        # 手动启动
        nohup java -jar $JAR_FILE > $LOG_FILE 2>&1 &
        echo "手动启动完成，PID: $!"
    fi
else
    # 手动启动
    echo "手动启动Java应用..."
    nohup java -jar $JAR_FILE > $LOG_FILE 2>&1 &
    NEW_PID=$!
    echo "启动完成，PID: $NEW_PID"
fi

# 6. 等待服务启动
echo -e "${YELLOW}6. 等待服务启动...${NC}"
sleep 10

# 7. 检查服务状态
echo -e "${YELLOW}7. 检查服务状态...${NC}"

# 检查进程
JAVA_PID=$(ps aux | grep "product-management-system" | grep -v grep | awk '{print $2}')
if [ ! -z "$JAVA_PID" ]; then
    echo -e "${GREEN}✅ Java进程运行中，PID: $JAVA_PID${NC}"
else
    echo -e "${RED}❌ Java进程未运行${NC}"
fi

# 检查端口
if netstat -tlnp | grep :8082 > /dev/null; then
    echo -e "${GREEN}✅ 端口8082已监听${NC}"
else
    echo -e "${RED}❌ 端口8082未监听${NC}"
fi

# 8. 测试API连接
echo -e "${YELLOW}8. 测试API连接...${NC}"
sleep 5

# 简单的健康检查
if curl -s http://localhost:8082/api/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ API健康检查通过${NC}"
else
    echo -e "${YELLOW}⚠️  API健康检查失败，可能服务还在启动中${NC}"
fi

# 9. 显示日志
echo -e "${YELLOW}9. 最近的日志输出:${NC}"
if [ -f "$LOG_FILE" ]; then
    echo "--- 最近20行日志 ---"
    tail -20 $LOG_FILE
else
    echo "日志文件不存在"
fi

echo
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}        重启完成                        ${NC}"
echo -e "${BLUE}========================================${NC}"
echo
echo "后续操作："
echo "1. 查看实时日志: tail -f $PROJECT_PATH/$LOG_FILE"
echo "2. 检查服务状态: systemctl status pms-backend"
echo "3. 测试API: curl http://localhost:8082/api/health"
echo "4. 重启Nginx: sudo systemctl restart nginx"
