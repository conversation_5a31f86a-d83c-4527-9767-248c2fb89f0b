<template>
  <div class="login-choice-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 主要内容 -->
    <div class="main-content">
      <!-- 标题 -->
      <div class="title-section">
        <h1 class="main-title">清风商城</h1>
        <p class="subtitle">品质生活，从这里开始</p>
        <div class="welcome-text">
          <p>🌟 发现精品好物 · 享受购物乐趣 🌟</p>
        </div>
      </div>

      <!-- 登录选择卡片 -->
      <div class="choice-cards">
        <!-- 用户登录卡片 -->
        <el-card
          class="choice-card user-card"
          shadow="hover"
          @click="handleUserLogin"
        >
          <div class="card-content">
            <div class="icon-wrapper">
              <div class="icon-bg">
                <el-icon class="card-icon">
                  <ShoppingCart />
                </el-icon>
              </div>
            </div>
            <h3 class="card-title">开始购物</h3>
            <p class="card-description">
              🛍️ 海量商品等你挑选<br>
              💝 优质服务贴心体验<br>
              🚀 快速下单便捷支付
            </p>
            <div class="features">
              <span class="feature-tag">新人专享</span>
              <span class="feature-tag">品质保证</span>
            </div>
            <el-button
              type="primary"
              class="login-btn user-btn"
              size="large"
            >
              <el-icon><Star /></el-icon>
              立即开始购物
            </el-button>
          </div>
        </el-card>
      </div>

      <!-- 底部信息 -->
      <div class="footer-info">
        <p>© 2024 清风商城 - 欢迎</p>
        <a href="#" @click.prevent="handleAdminLogin" class="admin-link">管理员入口</a>
      </div>
    </div>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'

export default {
  name: 'LoginChoice',
  setup() {
    const router = useRouter()

    const handleAdminLogin = () => {
      router.push('/admin/login')
    }

    const handleUserLogin = () => {
      router.push('/user/login')
    }

    return {
      handleAdminLogin,
      handleUserLogin
    }
  }
}
</script>

<style scoped>
/* 主容器 */
.login-choice-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  font-family: 'Helvetica Neue', Arial, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.floating-shape {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 8s ease-in-out infinite;
}

.shape-1 {
  width: 120px;
  height: 120px;
  top: 15%;
  left: 10%;
  animation-delay: 0s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 200, 100, 0.2));
}

.shape-2 {
  width: 80px;
  height: 80px;
  top: 70%;
  right: 20%;
  animation-delay: 2s;
  background: linear-gradient(45deg, rgba(255, 220, 150, 0.2), rgba(255, 255, 255, 0.1));
}

.shape-3 {
  width: 60px;
  height: 60px;
  bottom: 25%;
  left: 25%;
  animation-delay: 4s;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.2), rgba(255, 180, 80, 0.25));
}

.shape-4 {
  width: 100px;
  height: 100px;
  top: 40%;
  right: 10%;
  animation-delay: 6s;
  background: linear-gradient(45deg, rgba(255, 160, 60, 0.2), rgba(255, 255, 255, 0.15));
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg) scale(1);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-30px) rotate(180deg) scale(1.1);
    opacity: 0.3;
  }
}

/* 主要内容 */
.main-content {
  text-align: center;
  z-index: 1;
  max-width: 900px;
  width: 100%;
  padding: 0 20px;
}

/* 标题部分 */
.title-section {
  margin-bottom: 50px;
}

.main-title {
  font-size: 4.5rem;
  font-weight: 900;
  margin-bottom: 15px;
  letter-spacing: 4px;
  font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;

  /* 橙色渐变文字效果 */
  background: linear-gradient(135deg, #ffffff 0%, #fff5e6 30%, #ffcc80 70%, #ff8533 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  /* 增强的阴影效果 */
  filter: drop-shadow(2px 2px 4px rgba(255, 133, 51, 0.3))
          drop-shadow(0px 0px 20px rgba(255, 133, 51, 0.2));

  /* 文字描边效果 */
  -webkit-text-stroke: 1px rgba(255, 255, 255, 0.1);

  /* 动画效果 */
  animation: titleGlow 3s ease-in-out infinite alternate;
  transform: perspective(500px) rotateX(5deg);
}

.subtitle {
  font-size: 1.4rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 20px 0;
  font-weight: 400;
  letter-spacing: 1px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.welcome-text {
  margin-top: 15px;
}

.welcome-text p {
  font-size: 1.1rem;
  color: rgba(255, 255, 255, 0.85);
  margin: 0;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* 选择卡片容器 */
.choice-cards {
  display: flex;
  gap: 40px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;
}

/* 卡片样式 */
.choice-card {
  width: 380px;
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 24px;
  overflow: hidden;
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.98), rgba(255, 255, 255, 0.92));
  backdrop-filter: blur(20px);
  border: 3px solid transparent;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
}

.choice-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(145deg, #ff6a00, #ff8533, #ffb366);
  border-radius: 24px;
  padding: 3px;
  /* 使用更简单的边框效果替代复杂的mask */
  background-clip: padding-box;
  opacity: 0;
  transition: opacity 0.4s ease;
}

.choice-card:hover {
  transform: translateY(-15px) scale(1.02);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.choice-card:hover::before {
  opacity: 1;
}

/* 卡片内容 */
.card-content {
  padding: 45px 35px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.icon-wrapper {
  margin-bottom: 25px;
}

.icon-bg {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  box-shadow: 0 8px 24px rgba(255, 106, 0, 0.3);
  transition: all 0.4s ease;
}

.choice-card:hover .icon-bg {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 32px rgba(255, 106, 0, 0.4);
}

.card-icon {
  font-size: 2.5rem;
  color: #ffffff;
  transition: all 0.3s ease;
}

.card-title {
  font-size: 2rem;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 20px;
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.card-description {
  font-size: 1.1rem;
  color: #5a6c7d;
  line-height: 1.8;
  margin-bottom: 25px;
  font-weight: 400;
}

.features {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-bottom: 30px;
}

.feature-tag {
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3);
  transition: all 0.3s ease;
}

.feature-tag:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(255, 107, 107, 0.4);
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 56px;
  font-size: 1.2rem;
  font-weight: 700;
  border-radius: 28px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.user-btn {
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 100%);
  border: none;
  color: white;
  box-shadow: 0 8px 24px rgba(255, 106, 0, 0.3);
}

.user-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.6s ease;
}

.user-btn:hover::before {
  left: 100%;
}

.user-btn:hover {
  background: linear-gradient(135deg, #e55a00 0%, #ff7020 100%);
  transform: translateY(-3px);
  box-shadow: 0 12px 32px rgba(255, 106, 0, 0.4);
}

/* 底部信息 */
.footer-info {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1rem;
  margin-top: 20px;
}

.footer-info p {
  margin-bottom: 15px;
  font-weight: 300;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
}

/* 管理员登录链接 */
.admin-link {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  padding: 8px 16px;
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.admin-link:hover {
  color: rgba(255, 255, 255, 0.9);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-title {
    font-size: 3rem;
    letter-spacing: 2px;
  }

  .subtitle {
    font-size: 1.2rem;
  }

  .choice-cards {
    flex-direction: column;
    align-items: center;
    gap: 30px;
  }

  .choice-card {
    width: 320px;
  }

  .card-content {
    padding: 35px 25px;
  }

  .card-title {
    font-size: 1.8rem;
  }

  .icon-bg {
    width: 70px;
    height: 70px;
  }

  .card-icon {
    font-size: 2.2rem;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 2.5rem;
    letter-spacing: 1px;
  }

  .subtitle {
    font-size: 1.1rem;
  }

  .welcome-text p {
    font-size: 1rem;
  }

  .choice-card {
    width: 100%;
    max-width: 300px;
  }

  .card-content {
    padding: 30px 20px;
  }

  .card-title {
    font-size: 1.6rem;
  }

  .card-description {
    font-size: 1rem;
  }

  .features {
    flex-wrap: wrap;
    gap: 8px;
  }

  .feature-tag {
    font-size: 0.8rem;
    padding: 5px 12px;
  }

  .login-btn {
    height: 52px;
    font-size: 1.1rem;
  }
}

/* 标题发光动画 */
@keyframes titleGlow {
  0% {
    filter: drop-shadow(2px 2px 4px rgba(255, 133, 51, 0.3))
            drop-shadow(0px 0px 20px rgba(255, 133, 51, 0.2));
    transform: perspective(500px) rotateX(5deg) scale(1);
  }
  100% {
    filter: drop-shadow(3px 3px 6px rgba(255, 133, 51, 0.5))
            drop-shadow(0px 0px 30px rgba(255, 133, 51, 0.4));
    transform: perspective(500px) rotateX(5deg) scale(1.02);
  }
}

/* 标题悬停效果 */
.main-title:hover {
  animation-play-state: paused;
  filter: drop-shadow(4px 4px 8px rgba(255, 133, 51, 0.6))
          drop-shadow(0px 0px 40px rgba(255, 133, 51, 0.5));
  transform: perspective(500px) rotateX(5deg) scale(1.05);
  transition: all 0.3s ease;
}
</style>
