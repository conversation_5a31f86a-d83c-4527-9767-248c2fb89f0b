<template>
  <div class="product-detail-container">
    <!-- 顶部导航 -->
    <header class="detail-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBack"
          class="back-btn"
        >
          返回商城
        </el-button>
        <h1 class="page-title">商品详情</h1>
        <div class="header-actions">
          <el-button :icon="ShoppingCart" @click="goToCart">购物车</el-button>
        </div>
      </div>
    </header>

    <!-- 商品详情内容 -->
    <main class="detail-main" v-loading="loading">
      <div class="product-container" v-if="product">
        <!-- 商品图片区域 -->
        <div class="product-images">
          <div class="main-image">
            <img
              :src="getImageUrl(currentImage) || '/images/default-product.svg'"
              :alt="product.name"
              @error="handleImageError"
            />
          </div>
          <div class="image-thumbnails" v-if="product.images && product.images.length > 1">
            <div 
              v-for="(image, index) in product.images" 
              :key="index"
              class="thumbnail"
              :class="{ active: currentImage === image }"
              @click="currentImage = image"
            >
              <img :src="getImageUrl(image)" :alt="`商品图片${index + 1}`" />
            </div>
          </div>
        </div>

        <!-- 商品信息区域 -->
        <div class="product-info">
          <h1 class="product-title">{{ product.name }}</h1>
          <p class="product-subtitle">{{ product.subtitle }}</p>
          
          <div class="price-section">
            <span class="current-price">¥{{ product.price }}</span>
            <span class="original-price" v-if="product.originalPrice">¥{{ product.originalPrice }}</span>
            <span class="discount" v-if="product.discount">{{ product.discount }}折</span>
          </div>

          <div class="product-specs">
            <div class="spec-item" v-if="product.brand">
              <span class="spec-label">品牌：</span>
              <span class="spec-value">{{ product.brand }}</span>
            </div>
            <div class="spec-item" v-if="product.category">
              <span class="spec-label">分类：</span>
              <span class="spec-value">{{ product.category }}</span>
            </div>
            <div class="spec-item" v-if="product.stock !== undefined">
              <span class="spec-label">库存：</span>
              <span class="spec-value" :class="{ 'low-stock': product.stock < 10 }">
                {{ product.stock }}件
              </span>
            </div>
          </div>

          <!-- 购买数量选择 -->
          <div class="quantity-section">
            <span class="quantity-label">购买数量：</span>
            <el-input-number
              v-model="quantity"
              :min="1"
              :max="product.stock || 999"
              size="large"
              class="quantity-input"
            />
          </div>

          <!-- 操作按钮 -->
          <div class="action-buttons">
            <el-button 
              type="warning" 
              size="large" 
              :icon="ShoppingCart"
              @click="addToCart"
              :loading="addingToCart"
              class="add-cart-btn"
            >
              加入购物车
            </el-button>
            <el-button 
              type="danger" 
              size="large"
              @click="buyNow"
              class="buy-now-btn"
            >
              立即购买
            </el-button>
          </div>
        </div>
      </div>

      <!-- 商品详细描述 -->
      <div class="product-description" v-if="product">
        <el-tabs v-model="activeTab" class="detail-tabs">
          <el-tab-pane label="商品详情" name="description">
            <div class="description-content">
              <p v-if="product.description">{{ product.description }}</p>
              <div v-if="product.detailImages" class="detail-images">
                <img 
                  v-for="(image, index) in product.detailImages" 
                  :key="index"
                  :src="image" 
                  :alt="`详情图${index + 1}`"
                />
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="规格参数" name="specs">
            <div class="specs-content">
              <el-descriptions :column="2" border>
                <el-descriptions-item 
                  v-for="(value, key) in product.specifications" 
                  :key="key"
                  :label="key"
                >
                  {{ value }}
                </el-descriptions-item>
              </el-descriptions>
            </div>
          </el-tab-pane>
          <el-tab-pane label="用户评价" name="reviews">
            <div class="reviews-content">
              <p class="coming-soon">评价功能即将上线...</p>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, ShoppingCart } from '@element-plus/icons-vue'

export default {
  name: 'ProductDetailPage',
  components: {
    ArrowLeft,
    ShoppingCart
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 响应式数据
    const loading = ref(false)
    const product = ref(null)
    const currentImage = ref('')
    const quantity = ref(1)
    const addingToCart = ref(false)
    const activeTab = ref('description')
    
    // 获取商品详情
    const loadProductDetail = async () => {
      loading.value = true
      try {
        const productId = route.params.id || route.query.id
        
        // TODO: 调用API获取商品详情
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求
        
        // 模拟商品数据
        product.value = {
          id: productId,
          name: '茅台（MOUTAI）飞天 酱香型白酒 53度 500ml 单瓶装',
          subtitle: '正宗茅台酒，酱香浓郁，口感醇厚',
          price: 1299.00,
          originalPrice: 1599.00,
          discount: 8.1,
          brand: '茅台',
          category: '白酒',
          stock: 50,
          description: '茅台酒是中国的传统特产酒。与苏格兰威士忌、法国科涅克白兰地齐名的世界三大蒸馏名酒之一，同时是中国三大名酒"茅五剑"之一。也是大曲酱香型白酒的鼻祖，已有800多年的历史。',
          images: [
            '/images/maotai_500ml.jpg',
            '/images/maotai_500ml_2.jpg',
            '/images/maotai_500ml_3.jpg'
          ],
          detailImages: [
            '/images/maotai_detail_1.jpg',
            '/images/maotai_detail_2.jpg'
          ],
          specifications: {
            '品牌': '茅台',
            '产地': '贵州茅台镇',
            '香型': '酱香型',
            '度数': '53度',
            '净含量': '500ml',
            '包装': '单瓶装',
            '保质期': '长期',
            '储存条件': '阴凉干燥处'
          }
        }
        
        currentImage.value = product.value.images[0]
        
      } catch (error) {
        console.error('加载商品详情失败:', error)
        ElMessage.error('加载商品详情失败')
      } finally {
        loading.value = false
      }
    }
    
    // 返回商城
    const goBack = () => {
      router.push('/mall')
    }
    
    // 跳转到购物车
    const goToCart = () => {
      // TODO: 实现购物车页面跳转
      ElMessage.info('购物车功能开发中...')
    }
    
    // 加入购物车
    const addToCart = async () => {
      addingToCart.value = true
      try {
        // TODO: 调用加入购物车API
        await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟请求
        ElMessage.success(`已将${quantity.value}件商品加入购物车`)
      } catch (error) {
        ElMessage.error('加入购物车失败')
      } finally {
        addingToCart.value = false
      }
    }
    
    // 立即购买
    const buyNow = () => {
      // TODO: 跳转到订单确认页面
      ElMessage.info('立即购买功能开发中...')
    }
    
    // 获取图片URL
    const getImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return ''

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 根据环境动态设置服务器地址
      const serverUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:8082'
        : ''  // 生产环境使用相对路径，通过Nginx代理

      // 如果是相对路径，添加服务器地址
      return `${serverUrl}${imageUrl}`
    }

    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/default-product.svg'
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadProductDetail()
    })
    
    return {
      loading,
      product,
      currentImage,
      quantity,
      addingToCart,
      activeTab,
      goBack,
      goToCart,
      addToCart,
      buyNow,
      getImageUrl,
      handleImageError
    }
  }
}
</script>

<style scoped>
.product-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航样式 */
.detail-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-title {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
}

.header-actions .el-button {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
}

.header-actions .el-button:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 主要内容区域 */
.detail-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.product-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  padding: 30px;
  margin-bottom: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
}

/* 商品图片区域 */
.product-images {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.main-image {
  width: 100%;
  height: 400px;
  border-radius: 8px;
  overflow: hidden;
  background: #f8f9fa;
  border: 1px solid #eee;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 6px;
  overflow: hidden;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.3s ease;
}

.thumbnail.active {
  border-color: #667eea;
}

.thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 商品信息区域 */
.product-info {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.product-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
  line-height: 1.4;
}

.product-subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
  line-height: 1.5;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.current-price {
  font-size: 32px;
  font-weight: bold;
  color: #e74c3c;
}

.original-price {
  font-size: 18px;
  color: #999;
  text-decoration: line-through;
}

.discount {
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.product-specs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.spec-item {
  display: flex;
  align-items: center;
}

.spec-label {
  font-weight: 500;
  color: #666;
  min-width: 80px;
}

.spec-value {
  color: #333;
}

.spec-value.low-stock {
  color: #e74c3c;
  font-weight: bold;
}

.quantity-section {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 15px 0;
  border-bottom: 1px solid #eee;
}

.quantity-label {
  font-weight: 500;
  color: #666;
}

.quantity-input {
  width: 120px;
}

.action-buttons {
  display: flex;
  gap: 15px;
  margin-top: 10px;
}

.add-cart-btn,
.buy-now-btn {
  flex: 1;
  height: 50px;
  font-size: 16px;
  font-weight: bold;
  border-radius: 8px;
}

/* 商品详细描述区域 */
.product-description {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.detail-tabs {
  padding: 20px;
}

.description-content,
.specs-content,
.reviews-content {
  padding: 20px 0;
  min-height: 200px;
}

.detail-images {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-top: 20px;
}

.detail-images img {
  width: 100%;
  max-width: 600px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.coming-soon {
  text-align: center;
  color: #999;
  font-size: 16px;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    padding: 0 15px;
  }

  .detail-main {
    padding: 15px;
  }

  .product-container {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 20px;
  }

  .main-image {
    height: 300px;
  }

  .product-title {
    font-size: 20px;
  }

  .current-price {
    font-size: 28px;
  }

  .action-buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .product-container {
    padding: 15px;
  }

  .main-image {
    height: 250px;
  }

  .product-title {
    font-size: 18px;
  }

  .current-price {
    font-size: 24px;
  }
}
</style>
