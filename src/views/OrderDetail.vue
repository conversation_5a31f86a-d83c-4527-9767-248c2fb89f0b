<template>
  <div class="order-detail-container">
    <!-- 顶部导航 -->
    <header class="detail-header">
      <div class="header-content">
        <el-button 
          type="primary" 
          :icon="ArrowLeft" 
          @click="goBack"
          class="back-btn"
        >
          返回订单列表
        </el-button>
        <h1 class="page-title">订单详情</h1>
        <div class="header-placeholder"></div>
      </div>
    </header>

    <!-- 主要内容 -->
    <main class="detail-main">
      <div class="detail-content">
        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <el-skeleton :rows="8" animated />
        </div>

        <!-- 订单详情内容 -->
        <div v-else-if="orderDetail" class="order-detail-content">
          <!-- 订单基本信息 -->
          <div class="order-info-card">
            <div class="card-header">
              <h2>订单信息</h2>
              <el-tag :type="getStatusType(orderDetail.status)" size="large">
                {{ getStatusText(orderDetail.status) }}
              </el-tag>
            </div>
            
            <div class="order-basic-info">
              <div class="info-row">
                <span class="label">订单号：</span>
                <span class="value">{{ orderDetail.orderNo }}</span>
              </div>
              <div class="info-row">
                <span class="label">下单时间：</span>
                <span class="value">{{ formatTime(orderDetail.createTime) }}</span>
              </div>
              <div class="info-row" v-if="orderDetail.payTime">
                <span class="label">支付时间：</span>
                <span class="value">{{ formatTime(orderDetail.payTime) }}</span>
              </div>
              <div class="info-row">
                <span class="label">订单金额：</span>
                <span class="value amount">¥{{ orderDetail.totalAmount }}</span>
              </div>
            </div>
          </div>

          <!-- 商品列表 -->
          <div class="products-card">
            <div class="card-header">
              <h2>商品清单</h2>
              <span class="item-count">共 {{ orderDetail.items.length }} 件商品</span>
            </div>
            
            <div class="products-list">
              <div 
                v-for="item in orderDetail.items" 
                :key="item.itemId"
                class="product-item"
              >
                <div class="product-image">
                  <img 
                    :src="getProductImageUrl(item.imageUrl)" 
                    :alt="item.productName"
                    @error="handleImageError"
                  />
                </div>
                
                <div class="product-info">
                  <h3 class="product-name">{{ item.productName }}</h3>
                  <p class="product-desc">{{ item.description }}</p>
                  <div class="product-price">
                    <span class="price">¥{{ item.price }}</span>
                    <span class="quantity">× {{ item.quantity }}</span>
                  </div>
                </div>
                
                <div class="product-subtotal">
                  <span class="subtotal">¥{{ item.subtotal }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 订单操作 -->
          <div class="order-actions-card" v-if="orderDetail.status === 0 || orderDetail.status === 2">
            <div class="card-header">
              <h2>订单操作</h2>
            </div>
            
            <div class="actions-content">
              <el-button
                v-if="orderDetail.status === 0"
                size="large"
                @click="payOrder"
                :loading="paying"
              >
                立即支付
              </el-button>
              
              <el-button 
                v-if="orderDetail.status === 0"
                size="large"
                @click="cancelOrder"
                :loading="canceling"
              >
                取消订单
              </el-button>
              
              <el-button 
                v-if="orderDetail.status === 2"
                type="success"
                size="large"
                @click="confirmOrder"
                :loading="confirming"
              >
                确认收货
              </el-button>
            </div>
          </div>
        </div>

        <!-- 订单不存在 -->
        <div v-else class="empty-state">
          <el-empty description="订单不存在或已被删除">
            <el-button type="primary" @click="goBack">返回订单列表</el-button>
          </el-empty>
        </div>
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft } from '@element-plus/icons-vue'
import ordersAPI from '@/api/orders'

export default {
  name: 'OrderDetailPage',
  components: {
    ArrowLeft
  },
  setup() {
    const router = useRouter()
    const route = useRoute()
    
    // 响应式数据
    const loading = ref(false)
    const orderDetail = ref(null)
    const paying = ref(false)
    const canceling = ref(false)
    const confirming = ref(false)
    
    // 获取订单ID
    const orderId = route.params.id
    
    // 加载订单详情
    const loadOrderDetail = async () => {
      loading.value = true
      try {
        const response = await ordersAPI.getOrderDetail(orderId)
        if (response.success) {
          orderDetail.value = response.data
        } else {
          ElMessage.error(response.message || '获取订单详情失败')
        }
      } catch (error) {
        console.error('获取订单详情失败:', error)
        ElMessage.error('获取订单详情失败')
      } finally {
        loading.value = false
      }
    }
    
    // 返回订单列表
    const goBack = () => {
      router.push('/orders')
    }
    
    // 支付订单
    const payOrder = async () => {
      if (!orderDetail.value) return
      
      const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
      const payUrl = `${baseUrl}/api/alipay/pay?subject=${encodeURIComponent('订单支付')}&traceNo=${orderDetail.value.orderNo}&totalAmount=${orderDetail.value.totalAmount}`
      
      try {
        paying.value = true
        ElMessage.success('正在跳转到支付页面...')
        
        // 创建一个新的窗口来显示支付页面
        const payWindow = window.open('', '_blank')
        
        // 获取支付表单HTML
        const payResponse = await fetch(payUrl, {
          method: 'GET'
        })
        const payHtml = await payResponse.text()
        
        // 在新窗口中写入支付表单HTML
        payWindow.document.write(payHtml)
        payWindow.document.close()
      } catch (error) {
        console.error('跳转支付页面失败:', error)
        ElMessage.error('跳转支付页面失败')
      } finally {
        paying.value = false
      }
    }
    
    // 取消订单
    const cancelOrder = async () => {
      try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '取消订单', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        canceling.value = true
        const response = await ordersAPI.cancelOrder(orderId)
        
        if (response.success) {
          ElMessage.success('订单已取消')
          // 重新加载订单详情
          loadOrderDetail()
        } else {
          ElMessage.error(response.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          ElMessage.error('取消订单失败')
        }
      } finally {
        canceling.value = false
      }
    }
    
    // 确认收货
    const confirmOrder = async () => {
      try {
        await ElMessageBox.confirm('确定已收到商品并确认收货吗？', '确认收货', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })
        
        confirming.value = true
        const response = await ordersAPI.confirmOrder(orderId)
        
        if (response.success) {
          ElMessage.success('确认收货成功')
          // 重新加载订单详情
          loadOrderDetail()
        } else {
          ElMessage.error(response.message || '确认收货失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认收货失败:', error)
          ElMessage.error('确认收货失败')
        }
      } finally {
        confirming.value = false
      }
    }
    
    // 获取订单状态类型
    const getStatusType = (status) => {
      const statusMap = {
        0: 'warning',  // 待支付
        1: 'success',  // 已支付
        2: 'info',     // 已发货
        3: 'success',  // 已完成
        4: 'danger',   // 已取消
        5: 'warning'   // 已退款
      }
      return statusMap[status] || 'info'
    }
    
    // 获取订单状态文本
    const getStatusText = (status) => {
      const statusMap = {
        0: '待支付',
        1: '已支付',
        2: '已发货',
        3: '已完成',
        4: '已取消',
        5: '已退款'
      }
      return statusMap[status] || '未知状态'
    }
    
    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      })
    }
    
    // 获取商品图片URL
    const getProductImageUrl = (imageUrl) => {
      if (!imageUrl || imageUrl.trim() === '') return '/images/default-product.svg'

      // 如果已经是完整URL，直接返回
      if (imageUrl.startsWith('http')) {
        return imageUrl
      }

      // 如果是相对路径，添加服务器地址
      const serverUrl = process.env.NODE_ENV === 'development'
        ? 'http://localhost:8082'
        : ''  // 生产环境使用相对路径，通过Nginx代理
      return `${serverUrl}${imageUrl}`
    }
    
    // 图片加载错误处理
    const handleImageError = (event) => {
      event.target.src = '/images/default-product.svg'
    }
    
    // 组件挂载时加载数据
    onMounted(() => {
      loadOrderDetail()
    })
    
    return {
      loading,
      orderDetail,
      paying,
      canceling,
      confirming,
      goBack,
      payOrder,
      cancelOrder,
      confirmOrder,
      getStatusType,
      getStatusText,
      formatTime,
      getProductImageUrl,
      handleImageError,
      ArrowLeft
    }
  }
}
</script>

<style scoped>
/* CSS 自定义属性定义 - 与商城主页保持一致 */
:root {
  --jd-red: #ff6a00;
  --jd-red-hover: #ff5500;
  --jd-orange: #ff8533;
  --jd-text-primary: #333333;
  --jd-text-secondary: #666666;
  --jd-text-light: #999999;
  --jd-border: #e0e0e0;
}

.order-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航样式 - 与商城主页保持一致 */
.detail-header {
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 16px;
  transition: all 0.3s ease;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-1px);
}

.page-title {
  color: white;
  font-size: 24px;
  font-weight: 600;
  margin: 0;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.header-placeholder {
  width: 120px;
}

/* 主要内容样式 */
.detail-main {
  padding: 20px;
}

.detail-content {
  max-width: 1200px;
  margin: 0 auto;
}

.loading-container {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 卡片通用样式 */
.order-info-card,
.products-card,
.order-actions-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
  overflow: hidden;
}

.card-header {
  padding: 20px 30px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header h2 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* 订单基本信息样式 */
.order-basic-info {
  padding: 30px;
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-row .label {
  width: 100px;
  color: #666;
  font-size: 14px;
}

.info-row .value {
  color: #333;
  font-size: 14px;
  font-weight: 500;
}

.info-row .value.amount {
  color: var(--jd-red);
  font-size: 18px;
  font-weight: 600;
}

.item-count {
  color: #666;
  font-size: 14px;
}

/* 商品列表样式 */
.products-list {
  padding: 0 30px 30px;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.product-item:last-child {
  border-bottom: none;
}

.product-image {
  width: 80px;
  height: 80px;
  margin-right: 20px;
  flex-shrink: 0;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.product-info {
  flex: 1;
  margin-right: 20px;
}

.product-name {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
  line-height: 1.4;
}

.product-desc {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #666;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 10px;
}

.product-price .price {
  color: var(--jd-red);
  font-size: 16px;
  font-weight: 600;
}

.product-price .quantity {
  color: #666;
  font-size: 14px;
}

.product-subtotal {
  text-align: right;
  min-width: 100px;
}

.subtotal {
  color: var(--jd-red);
  font-size: 18px;
  font-weight: 600;
}

/* 订单操作样式 */
.actions-content {
  padding: 30px;
  display: flex;
  gap: 15px;
  justify-content: center;
}

.actions-content .el-button {
  min-width: 120px;
  font-weight: 500;
  transition: all 0.3s ease;
}

/* 统一按钮样式 - 橙色主题边框风格 - 使用更强的选择器 */
.order-detail-container .actions-content .el-button.el-button--primary,
.order-detail-container .actions-content .el-button.el-button--default {
  background: white !important;
  border: 2px solid var(--jd-red) !important;
  color: var(--jd-red) !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
}

.order-detail-container .actions-content .el-button.el-button--primary span,
.order-detail-container .actions-content .el-button.el-button--default span {
  color: var(--jd-red) !important;
}

.order-detail-container .actions-content .el-button.el-button--primary:hover,
.order-detail-container .actions-content .el-button.el-button--default:hover {
  background: var(--jd-red) !important;
  border-color: var(--jd-red) !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3) !important;
}

.order-detail-container .actions-content .el-button.el-button--primary:hover span,
.order-detail-container .actions-content .el-button.el-button--default:hover span {
  color: white !important;
}

.order-detail-container .actions-content .el-button.el-button--primary:focus,
.order-detail-container .actions-content .el-button.el-button--default:focus {
  background: var(--jd-red) !important;
  border-color: var(--jd-red) !important;
  color: white !important;
}

.order-detail-container .actions-content .el-button.el-button--primary:focus span,
.order-detail-container .actions-content .el-button.el-button--default:focus span {
  color: white !important;
}

/* 确保按钮在所有状态下都有正确的样式 */
.order-detail-container .actions-content .el-button.el-button--primary:not(:hover):not(:focus),
.order-detail-container .actions-content .el-button.el-button--default:not(:hover):not(:focus) {
  background-color: white !important;
  background: white !important;
  border-color: var(--jd-red) !important;
  color: var(--jd-red) !important;
}

.order-detail-container .actions-content .el-button.el-button--primary:not(:hover):not(:focus) span,
.order-detail-container .actions-content .el-button.el-button--default:not(:hover):not(:focus) span {
  color: var(--jd-red) !important;
}

/* 额外的强制样式覆盖 - 针对Element Plus的深层样式 */
.order-detail-container .actions-content .el-button {
  background: white !important;
  border: 2px solid var(--jd-red) !important;
  color: var(--jd-red) !important;
}

.order-detail-container .actions-content .el-button span {
  color: var(--jd-red) !important;
}

.order-detail-container .actions-content .el-button:hover {
  background: var(--jd-red) !important;
  border-color: var(--jd-red) !important;
  color: white !important;
}

.order-detail-container .actions-content .el-button:hover span {
  color: white !important;
}

/* 统一按钮样式 - 清除所有Element Plus默认样式 */
.order-detail-container .actions-content :deep(.el-button) {
  /* 重置所有可能的样式 */
  background: white !important;
  background-color: white !important;
  background-image: none !important;
  border: 2px solid #ff6a00 !important;
  border-color: #ff6a00 !important;
  color: #ff6a00 !important;
  font-weight: 500 !important;
  transition: all 0.3s ease !important;
  /* 清除可能的阴影和渐变 */
  box-shadow: none !important;
  text-shadow: none !important;
}

/* 按钮内文字样式 */
.order-detail-container .actions-content :deep(.el-button span) {
  color: #ff6a00 !important;
}

/* 悬停状态 */
.order-detail-container .actions-content :deep(.el-button:hover) {
  background: #ff6a00 !important;
  background-color: #ff6a00 !important;
  background-image: none !important;
  border: 2px solid #ff6a00 !important;
  border-color: #ff6a00 !important;
  color: white !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3) !important;
}

.order-detail-container .actions-content :deep(.el-button:hover span) {
  color: white !important;
}

/* 焦点状态 */
.order-detail-container .actions-content :deep(.el-button:focus) {
  background: #ff6a00 !important;
  background-color: #ff6a00 !important;
  background-image: none !important;
  border: 2px solid #ff6a00 !important;
  border-color: #ff6a00 !important;
  color: white !important;
  box-shadow: 0 4px 12px rgba(255, 106, 0, 0.3) !important;
}

.order-detail-container .actions-content :deep(.el-button:focus span) {
  color: white !important;
}

/* 活动状态 */
.order-detail-container .actions-content :deep(.el-button:active) {
  background: #e55a00 !important;
  background-color: #e55a00 !important;
  border-color: #e55a00 !important;
  transform: translateY(0) !important;
}

/* 禁用状态 */
.order-detail-container .actions-content :deep(.el-button:disabled) {
  background: #f5f5f5 !important;
  background-color: #f5f5f5 !important;
  border-color: #d9d9d9 !important;
  color: #c0c4cc !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

.order-detail-container .actions-content :deep(.el-button:disabled span) {
  color: #c0c4cc !important;
}

/* 危险操作按钮样式 */
.actions-content .el-button--danger {
  background: #ff4757;
  border: none;
  color: white;
}

.actions-content .el-button--danger:hover {
  background: #ff3742;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(255, 71, 87, 0.3);
}

/* 空状态样式 */
.empty-state {
  background: white;
  border-radius: 12px;
  padding: 60px 30px;
  text-align: center;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-main {
    padding: 10px;
  }

  .header-content {
    padding: 0 15px;
  }

  .page-title {
    font-size: 20px;
  }

  .card-header,
  .order-basic-info,
  .products-list,
  .actions-content {
    padding: 20px;
  }

  .product-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .product-image {
    margin-right: 0;
  }

  .product-info {
    margin-right: 0;
    width: 100%;
  }

  .product-subtotal {
    text-align: left;
    min-width: auto;
  }

  .actions-content {
    flex-direction: column;
    align-items: center;
  }

  .actions-content .el-button {
    width: 100%;
    max-width: 300px;
  }
}
</style>
