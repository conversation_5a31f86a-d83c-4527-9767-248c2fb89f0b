<template>
  <div class="orders-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <el-button
          type="text"
          @click="goToMall"
          class="back-btn"
        >
          <el-icon><ArrowLeft /></el-icon>
          返回商城
        </el-button>
        <h1 class="page-title">我的订单</h1>
        <div class="header-spacer"></div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="orders-main">
      <!-- 订单统计 -->
      <div class="order-stats" v-if="statistics">
        <div class="stat-item">
          <div class="stat-number">{{ statistics.totalCount || 0 }}</div>
          <div class="stat-label">全部订单</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ statistics.statusCounts?.pending || 0 }}</div>
          <div class="stat-label">待支付</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ statistics.statusCounts?.paid || 0 }}</div>
          <div class="stat-label">已支付</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ statistics.statusCounts?.shipped || 0 }}</div>
          <div class="stat-label">已发货</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ statistics.statusCounts?.completed || 0 }}</div>
          <div class="stat-label">已完成</div>
        </div>
      </div>

      <!-- 订单列表 -->
      <div class="orders-list">
      <div v-if="loading" class="loading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <span>加载中...</span>
      </div>

      <div v-else-if="orders.length === 0" class="empty-state">
        <el-icon><ShoppingCart /></el-icon>
        <p>暂无订单</p>
        <el-button type="primary" @click="goToMall">去购物</el-button>
      </div>

      <div v-else>
        <div 
          v-for="order in orders" 
          :key="order.id" 
          class="order-card"
        >
          <div class="order-header">
            <div class="order-info">
              <span class="order-no">订单号：{{ order.order_no }}</span>
              <span class="order-time">{{ formatTime(order.create_time) }}</span>
            </div>
            <div class="order-status">
              <el-tag :type="getStatusType(order.status)">
                {{ getStatusText(order.status) }}
              </el-tag>
            </div>
          </div>

          <div class="order-content">
            <div class="order-amount">
              <span class="amount-label">订单金额：</span>
              <span class="amount-value">¥{{ order.total_amount }}</span>
            </div>
            <div class="order-items">
              <span>共 {{ order.item_count }} 件商品</span>
            </div>
          </div>

          <div class="order-actions">
            <el-button 
              size="small" 
              @click="viewOrderDetail(order.id)"
            >
              查看详情
            </el-button>
            
            <el-button
              v-if="order.status === 0"
              type="primary"
              size="small"
              @click="payOrder(order)"
            >
              立即支付
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="success"
              size="small"
              @click="simulatePaySuccess(order)"
            >
              模拟支付成功
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="info"
              size="small"
              @click="queryPayStatus(order)"
            >
              查询支付状态
            </el-button>

            <el-button
              v-if="order.status === 0"
              type="warning"
              size="small"
              @click="forceConfirmPay(order)"
            >
              强制确认支付（测试）
            </el-button>

            <el-button
              v-if="order.status === 0"
              size="small"
              @click="cancelOrder(order.id)"
            >
              取消订单
            </el-button>

            <el-button
              v-if="order.status === 1"
              type="danger"
              size="small"
              @click="refundOrder(order)"
            >
              申请退款
            </el-button>
            
            <el-button 
              v-if="order.status === 2" 
              type="success" 
              size="small"
              @click="confirmOrder(order.id)"
            >
              确认收货
            </el-button>
          </div>
        </div>
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowLeft, ShoppingCart, Loading } from '@element-plus/icons-vue'
import ordersAPI from '@/api/orders'

export default {
  name: 'OrdersList',
  components: {
    ArrowLeft,
    ShoppingCart,
    Loading
  },
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const orders = ref([])
    const statistics = ref(null)

    // 获取当前用户ID（从localStorage获取）
    const getCurrentUserId = () => {
      try {
        const userInfo = localStorage.getItem('userInfo')
        if (userInfo) {
          const userData = JSON.parse(userInfo)
          return userData.id
        }
        return null
      } catch (error) {
        console.error('获取用户ID失败:', error)
        return null
      }
    }

    // 返回商城
    const goToMall = () => {
      router.push('/mall')
    }

    // 加载订单列表
    const loadOrders = async () => {
      loading.value = true
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }
        
        const response = await ordersAPI.getUserOrders(userId)
        if (response.success) {
          orders.value = response.data
        } else {
          ElMessage.error(response.message || '加载订单失败')
        }
      } catch (error) {
        console.error('加载订单失败:', error)
        ElMessage.error('加载订单失败')
      } finally {
        loading.value = false
      }
    }

    // 加载订单统计
    const loadStatistics = async () => {
      try {
        const userId = getCurrentUserId()

        if (!userId) {
          return // 如果用户未登录，直接返回，不显示错误（因为会在loadOrders中处理）
        }
        
        const response = await ordersAPI.getOrderStatistics(userId)
        if (response.success) {
          statistics.value = response.data
        }
      } catch (error) {
        console.error('加载订单统计失败:', error)
      }
    }

    // 查看订单详情
    const viewOrderDetail = (orderId) => {
      router.push(`/order-detail/${orderId}`)
    }

    // 支付订单
    const payOrder = async (order) => {
      const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
      const payUrl = `${baseUrl}/api/alipay/pay?subject=${encodeURIComponent('订单支付')}&traceNo=${order.order_no}&totalAmount=${order.total_amount}`

      try {
        ElMessage.success('正在跳转到支付页面...')

        // 创建一个新的窗口来显示支付页面
        const payWindow = window.open('', '_blank')

        // 获取支付表单HTML
        const payResponse = await fetch(payUrl, {
          method: 'GET'
        })
        const payHtml = await payResponse.text()

        // 在新窗口中写入支付表单HTML
        payWindow.document.write(payHtml)
        payWindow.document.close()
      } catch (error) {
        console.error('跳转支付页面失败:', error)
        ElMessage.error('跳转支付页面失败')
      }
    }

    // 模拟支付成功（用于测试）
    const simulatePaySuccess = async (order) => {
      try {
        await ElMessageBox.confirm('确定要模拟支付成功吗？这将更新订单状态为已支付。', '模拟支付', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        // 调用测试回调接口
        const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
        const response = await fetch(`${baseUrl}/api/alipay/test-callback`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no,
            tradeStatus: 'TRADE_SUCCESS'
          })
        })

        const result = await response.json()

        if (result.success) {
          ElMessage.success('支付状态更新成功！')
          // 重新加载订单列表和统计
          loadOrders()
          loadStatistics()
        } else {
          ElMessage.error(result.message || '支付状态更新失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('模拟支付失败:', error)
          ElMessage.error('模拟支付失败')
        }
      }
    }

    // 查询支付状态（调用支付宝API查询真实状态）
    const queryPayStatus = async (order) => {
      try {
        ElMessage.info('正在查询支付状态...')

        // 调用查询支付状态接口
        const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
        const response = await fetch(`${baseUrl}/api/alipay/query-pay-status`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no
          })
        })

        const result = await response.json()

        if (result.success) {
          if (result.alreadyPaid) {
            ElMessage.info('订单已经是支付状态')
          } else if (result.updated) {
            ElMessage.success(`支付状态查询成功！订单已支付（${result.tradeStatus}）`)
            // 重新加载订单列表和统计
            loadOrders()
            loadStatistics()
          } else {
            ElMessage.warning(`订单尚未支付（${result.tradeStatus || '未知状态'}）`)
          }
        } else {
          ElMessage.error(result.message || '查询支付状态失败')
        }
      } catch (error) {
        console.error('查询支付状态失败:', error)
        ElMessage.error('查询支付状态失败')
      }
    }

    // 强制确认支付状态（仅用于开发测试）
    const forceConfirmPay = async (order) => {
      try {
        await ElMessageBox.confirm(
          '这是开发测试功能，会强制将订单标记为已支付状态。确定要继续吗？',
          '强制确认支付（开发测试）',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        ElMessage.info('正在强制确认支付状态...')

        // 调用强制确认支付状态接口
        const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
        const response = await fetch(`${baseUrl}/api/alipay/force-confirm-pay`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no
          })
        })

        const result = await response.json()

        if (result.success) {
          if (result.alreadyPaid) {
            ElMessage.info('订单已经是支付状态')
          } else if (result.updated) {
            ElMessage.success('支付状态强制确认成功！（开发测试）')
            // 重新加载订单列表和统计
            loadOrders()
            loadStatistics()
          }
        } else {
          ElMessage.error(result.message || '强制确认失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('强制确认支付状态失败:', error)
          ElMessage.error('强制确认失败')
        }
      }
    }

    // 取消订单
    const cancelOrder = async (orderId) => {
      try {
        await ElMessageBox.confirm('确定要取消这个订单吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await ordersAPI.cancelOrder(orderId, userId)
        
        if (response.success) {
          ElMessage.success('订单取消成功')
          loadOrders() // 重新加载订单列表
          loadStatistics() // 重新加载统计
        } else {
          ElMessage.error(response.message || '取消订单失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('取消订单失败:', error)
          ElMessage.error('取消订单失败')
        }
      }
    }

    // 确认收货
    const confirmOrder = async (orderId) => {
      try {
        await ElMessageBox.confirm('确定要确认收货吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'info'
        })

        const userId = getCurrentUserId()

        if (!userId) {
          ElMessage.error('用户未登录，请先登录')
          router.push('/user-login')
          return
        }

        const response = await ordersAPI.confirmOrder(orderId, userId)
        
        if (response.success) {
          ElMessage.success('确认收货成功')
          loadOrders() // 重新加载订单列表
          loadStatistics() // 重新加载统计
        } else {
          ElMessage.error(response.message || '确认收货失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error('确认收货失败:', error)
          ElMessage.error('确认收货失败')
        }
      }
    }

    // 申请退款
    const refundOrder = async (order) => {
      try {
        // 弹出退款原因输入框
        const { value: refundReason } = await ElMessageBox.prompt('请输入退款原因', '申请退款', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /.+/,
          inputErrorMessage: '退款原因不能为空',
          inputPlaceholder: '请输入退款原因，如：商品质量问题、不需要了等'
        })

        if (!refundReason) {
          return
        }

        // 确认退款
        await ElMessageBox.confirm(
          `确定要申请退款吗？\n订单号：${order.order_no}\n退款金额：¥${order.total_amount}\n退款原因：${refundReason}`,
          '确认退款',
          {
            confirmButtonText: '确定退款',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        ElMessage.info('正在处理退款申请...')

        // 调用退款接口
        const baseUrl = process.env.NODE_ENV === 'development' ? 'http://localhost:8082' : 'http://*************:8082'
        const response = await fetch(`${baseUrl}/api/alipay/refund`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            orderNo: order.order_no,
            refundAmount: order.total_amount,
            refundReason: refundReason
          })
        })

        const result = await response.json()

        if (result.success) {
          ElMessage.success('退款申请成功！退款将在1-3个工作日内到账')
          // 重新加载订单列表和统计
          loadOrders()
          loadStatistics()
        } else {
          ElMessage.error(result.message || '退款申请失败')
        }

      } catch (error) {
        console.error('退款申请失败:', error)
        if (error !== 'cancel') {
          ElMessage.error('退款申请失败')
        }
      }
    }

    // 获取状态类型
    const getStatusType = (status) => {
      const statusTypes = {
        0: 'warning',  // 待支付
        1: 'info',     // 已支付
        2: 'primary',  // 已发货
        3: 'success',  // 已完成
        4: 'danger',   // 已取消
        5: 'warning'   // 已退款
      }
      return statusTypes[status] || 'info'
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusTexts = {
        0: '待支付',
        1: '已支付',
        2: '已发货',
        3: '已完成',
        4: '已取消',
        5: '已退款'
      }
      return statusTexts[status] || '未知状态'
    }

    // 格式化时间
    const formatTime = (timeStr) => {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return date.toLocaleString('zh-CN')
    }

    // 页面加载时获取数据
    onMounted(async () => {
      // 直接加载订单数据，不自动检查支付状态
      loadOrders()
      loadStatistics()

      // 检查是否有支付成功的参数
      const urlParams = new URLSearchParams(window.location.search)
      const paySuccess = urlParams.get('paySuccess')
      const orderNo = urlParams.get('orderNo')

      if (paySuccess === 'true' && orderNo) {
        ElMessage.success(`订单 ${orderNo} 支付成功！`)
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
      } else if (paySuccess === 'false') {
        ElMessage.error('支付失败，请重试')
        // 清除URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    })

    return {
      loading,
      orders,
      statistics,
      goToMall,
      loadOrders,
      viewOrderDetail,
      payOrder,
      simulatePaySuccess,
      queryPayStatus,
      forceConfirmPay,
      cancelOrder,
      confirmOrder,
      refundOrder,
      getStatusType,
      getStatusText,
      formatTime
    }
  }
}
</script>

<style scoped>
.orders-container {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  background: linear-gradient(135deg, #ff6a00 0%, #ff8533 50%, #ffb366 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 70px;
  position: relative;
}

.back-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.back-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.page-title {
  color: white;
  font-size: 20px;
  font-weight: bold;
  margin: 0;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  white-space: nowrap;
}

.header-spacer {
  width: 100px;
  flex-shrink: 0;
}

/* 主要内容区域 */
.orders-main {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.order-stats {
  background: white;
  margin-bottom: 20px;
  padding: 30px;
  border-radius: 12px;
  display: flex;
  justify-content: space-around;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.stat-item {
  text-align: center;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #ff6a00;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.orders-list {
  background: transparent;
  border-radius: 0;
  box-shadow: none;
  overflow: visible;
}

.loading {
  text-align: center;
  padding: 40px;
  color: #666;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
}

.loading .el-icon {
  font-size: 24px;
  margin-right: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  color: #666;
}

.empty-state .el-icon {
  font-size: 64px;
  color: #ddd;
  margin-bottom: 16px;
}

.order-card {
  background: white;
  padding: 20px;
  margin-bottom: 10px;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.order-card:hover {
  background: #f8f9fa;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.order-card:last-child {
  margin-bottom: 0;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.order-no {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.order-time {
  font-size: 13px;
  color: #666;
}

.order-content {
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-amount {
  display: flex;
  align-items: center;
  gap: 8px;
}

.amount-label {
  color: #666;
  font-size: 14px;
}

.amount-value {
  font-size: 20px;
  font-weight: bold;
  color: #ff6a00;
}

.order-items {
  font-size: 14px;
  color: #666;
  background: #f8f9fa;
  padding: 8px 12px;
  border-radius: 6px;
}

.order-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

/* 订单操作按钮样式 */
.order-actions .el-button {
  border-radius: 20px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.order-actions .el-button--primary {
  background: linear-gradient(135deg, #ff6a00, #ff8533);
  border: none;
}

.order-actions .el-button--primary:hover {
  background: linear-gradient(135deg, #ff5500, #ff7722);
  transform: translateY(-1px);
}

.order-actions .el-button--success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border: none;
}

.order-actions .el-button--success:hover {
  background: linear-gradient(135deg, #5daf34, #73c556);
  transform: translateY(-1px);
}

.order-actions .el-button--info {
  background: linear-gradient(135deg, #909399, #b1b3b8);
  border: none;
}

.order-actions .el-button--info:hover {
  background: linear-gradient(135deg, #82848a, #a6a9ad);
  transform: translateY(-1px);
}

.order-actions .el-button--warning {
  background: linear-gradient(135deg, #e6a23c, #f0c78a);
  border: none;
}

.order-actions .el-button--warning:hover {
  background: linear-gradient(135deg, #cf9236, #e8bb7a);
  transform: translateY(-1px);
}

.order-actions .el-button--danger {
  background: linear-gradient(135deg, #f56c6c, #f89898);
  border: none;
}

.order-actions .el-button--danger:hover {
  background: linear-gradient(135deg, #f25555, #f78888);
  transform: translateY(-1px);
}

@media (max-width: 768px) {
  .orders-main {
    padding: 10px;
  }

  .order-stats {
    margin-bottom: 15px;
    padding: 20px 15px;
  }

  .stat-number {
    font-size: 24px;
  }

  .order-card {
    padding: 15px;
  }

  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .order-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .order-actions {
    flex-wrap: wrap;
    justify-content: flex-start;
    gap: 8px;
  }

  .order-actions .el-button {
    font-size: 12px;
    padding: 6px 12px;
  }
}
</style>
