import axios from 'axios'

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // 如果有环境变量配置的API地址，优先使用
  if (process.env.VUE_APP_API_BASE_URL) {
    return process.env.VUE_APP_API_BASE_URL
  }

  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:8082/api'
  }

  // 生产环境 - 使用相对路径，通过Nginx代理
  return '/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(), // Spring Boot后端地址
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 在发送请求之前做些什么
    console.log('发送请求:', config)
    return config
  },
  error => {
    // 对请求错误做些什么
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 对响应数据做点什么
    console.log('收到响应:', response)
    return response.data
  },
  error => {
    // 对响应错误做点什么
    console.error('响应错误:', error)
    
    if (error.response) {
      // 服务器返回了错误状态码
      const { status, data } = error.response
      switch (status) {
        case 400:
          console.error('请求参数错误')
          break
        case 401:
          console.error('未授权访问')
          break
        case 403:
          console.error('禁止访问')
          break
        case 404:
          console.error('请求的资源不存在')
          break
        case 500:
          console.error('服务器内部错误')
          break
        default:
          console.error(`请求失败，状态码: ${status}`)
      }
      return Promise.reject(data || error.response)
    } else if (error.request) {
      // 请求已发出但没有收到响应
      console.error('网络错误，请检查网络连接')
      return Promise.reject({ message: '网络错误，请检查网络连接' })
    } else {
      // 其他错误
      console.error('请求配置错误:', error.message)
      return Promise.reject({ message: error.message })
    }
  }
)

// 用户API
export const userAPI = {
  // 用户注册
  register(userData) {
    return api.post('/user/register', userData)
  },

  // 用户登录
  login(loginData) {
    return api.post('/user/login', loginData)
  },

  // 检查用户名是否可用
  checkUsername(username) {
    return api.get('/user/check-username', {
      params: { username }
    })
  },

  // 检查邮箱是否可用
  checkEmail(email) {
    return api.get('/user/check-email', {
      params: { email }
    })
  },

  // 获取用户信息
  getUserProfile(userId) {
    return api.get(`/user/profile/${userId}`)
  },

  // 更新用户信息
  updateUserProfile(userId, userData) {
    return api.put(`/user/profile/${userId}`, userData)
  },

  // 修改密码
  changePassword(userId, passwordData) {
    return api.put(`/user/change-password/${userId}`, passwordData)
  }
}

// 管理员API
export const adminAPI = {
  // 管理员登录
  login(loginData) {
    return api.post('/admin/login', loginData)
  },

  // 检查管理员权限
  checkPermission(username) {
    return api.get('/admin/check-permission', {
      params: { username }
    })
  },

  // 获取管理员信息
  getAdminInfo(adminId) {
    return api.get('/admin/info', {
      params: { adminId }
    })
  },

  // 管理员修改密码
  changePassword(passwordData) {
    return api.post('/admin/change-password', passwordData)
  }
}

// 购物车API
export const cartAPI = {
  // 获取用户购物车
  getCart(userId) {
    return api.get(`/cart/list/${userId}`)
  },

  // 添加商品到购物车
  addToCart(userId, productId, quantity = 1) {
    return api.post('/cart/add', {
      userId,
      productId,
      quantity
    })
  },

  // 更新购物车商品数量
  updateQuantity(cartId, quantity) {
    return api.put('/cart/update', {
      cartId,
      quantity
    })
  },

  // 从购物车删除商品
  removeFromCart(userId, productId) {
    return api.delete('/cart/remove', {
      data: {
        userId,
        productId
      }
    })
  },

  // 批量删除购物车商品
  batchRemoveFromCart(userId, productIds) {
    return api.delete('/cart/batch-remove', {
      data: {
        userId,
        productIds
      }
    })
  },

  // 清空购物车
  clearCart(userId) {
    return api.delete(`/cart/clear/${userId}`)
  },

  // 获取购物车统计信息
  getCartInfo(userId) {
    return api.get(`/cart/info/${userId}`)
  }
}

// 订单API
export const ordersAPI = {
  // 从购物车创建订单
  createOrderFromCart(userId, cartItemIds) {
    return api.post('/orders/create-from-cart', {
      userId,
      cartItemIds
    })
  },

  // 直接购买创建订单
  createOrderDirect(userId, productId, quantity) {
    return api.post('/orders/create-direct', {
      userId,
      productId,
      quantity
    })
  },

  // 获取用户订单列表
  getUserOrders(userId) {
    return api.get(`/orders/list/${userId}`)
  },

  // 获取订单详情
  getOrderDetail(orderId) {
    return api.get(`/orders/detail/${orderId}`)
  },

  // 取消订单
  cancelOrder(orderId, userId) {
    return api.put(`/orders/cancel/${orderId}`, {
      userId
    })
  },

  // 确认收货
  confirmOrder(orderId, userId) {
    return api.put(`/orders/confirm/${orderId}`, {
      userId
    })
  },

  // 获取订单统计
  getOrderStatistics(userId) {
    return api.get(`/orders/statistics/${userId}`)
  },

  // ========================================
  // 管理员订单管理API
  // ========================================

  // 获取所有订单列表（管理员）
  getAllOrdersForAdmin() {
    return api.get('/orders/admin/list')
  },

  // 更新订单状态（管理员）
  updateOrderStatusByAdmin(orderId, status, remark) {
    return api.put(`/orders/admin/status/${orderId}`, {
      status,
      remark
    })
  },

  // 获取订单统计信息（管理员）
  getAdminOrderStatistics() {
    return api.get('/orders/admin/statistics')
  },

  // 搜索订单列表（管理员）
  searchOrdersForAdmin(keyword, status) {
    const params = new URLSearchParams()
    if (keyword && keyword.trim()) {
      params.append('keyword', keyword.trim())
    }
    if (status !== null && status !== undefined && status !== '') {
      params.append('status', status)
    }

    const queryString = params.toString()
    const url = queryString ? `/orders/admin/search?${queryString}` : '/orders/admin/search'

    return api.get(url)
  },

  // 根据订单号查询订单
  getOrderByOrderNo(orderNo) {
    return api.get(`/orders/order-no/${orderNo}`)
  }
}

export default api
