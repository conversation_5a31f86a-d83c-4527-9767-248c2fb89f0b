import axios from 'axios'

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // 如果有环境变量配置的API地址，优先使用
  if (process.env.VUE_APP_API_BASE_URL) {
    return process.env.VUE_APP_API_BASE_URL
  }

  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return process.env.NODE_ENV === 'development'
      ? 'http://localhost:8082/api'
      : '/api'  // 生产环境使用相对路径，通过Nginx代理
  }

  // 生产环境 - 使用相对路径，通过Nginx代理
  return '/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加管理员token
    const adminToken = localStorage.getItem('adminToken')
    if (adminToken) {
      config.headers.Authorization = `Bearer ${adminToken}`
    }
    
    console.log('管理员API请求:', config.method.toUpperCase(), config.url, config.data)
    return config
  },
  error => {
    console.error('管理员API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('管理员API响应:', response.config.url, response.data)
    return response.data
  },
  error => {
    console.error('管理员API响应错误:', error.response?.data || error.message)

    // 处理401未授权错误
    if (error.response?.status === 401) {
      localStorage.removeItem('adminToken')
      localStorage.removeItem('adminInfo')
      localStorage.removeItem('isAdminLoggedIn')
      window.location.href = '/admin/login'
    }

    return Promise.reject(error.response?.data || error)
  }
)

// 管理员API接口
const adminAPI = {
  // 获取所有管理员
  getAllAdmins() {
    return api.get('/admin/list')
  },

  // 添加管理员
  addAdmin(adminData) {
    return api.post('/admin/add', {
      username: adminData.username,
      email: adminData.email,
      phone: adminData.phone,
      password: adminData.password,
      status: adminData.status || 2
    })
  },

  // 更新管理员
  updateAdmin(adminData) {
    return api.put(`/admin/update/${adminData.id}`, {
      email: adminData.email,
      status: adminData.status
    })
  },

  // 删除管理员
  deleteAdmin(adminId) {
    return api.delete(`/admin/delete/${adminId}`)
  },

  // 获取管理员详情
  getAdminById(adminId) {
    return api.get(`/admin/${adminId}`)
  },

  // 重置管理员密码
  resetAdminPassword(adminId, newPassword) {
    return api.put(`/admin/reset-password/${adminId}`, {
      password: newPassword
    })
  },

  // 管理员登录
  login(loginData) {
    return api.post('/admin/login', {
      username: loginData.username,
      password: loginData.password
    })
  },

  // 管理员登出
  logout() {
    return api.post('/admin/logout')
  },

  // 获取当前管理员信息
  getCurrentAdmin() {
    return api.get('/admin/current')
  },

  // 更新当前管理员信息
  updateCurrentAdmin(adminData) {
    return api.put('/admin/profile', {
      email: adminData.email,
      oldPassword: adminData.oldPassword,
      newPassword: adminData.newPassword
    })
  },

  // 检查用户名是否存在
  checkUsername(username) {
    return api.get(`/admin/check-username?username=${username}`)
  },

  // 检查邮箱是否存在
  checkEmail(email) {
    return api.get(`/admin/check-email?email=${email}`)
  },

  // 管理员修改密码
  changePassword(passwordData) {
    return api.post('/admin/change-password', {
      username: passwordData.username,
      oldPassword: passwordData.oldPassword,
      newPassword: passwordData.newPassword
    })
  }
}

export default adminAPI
