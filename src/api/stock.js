import axios from 'axios'

// 根据环境变量确定API基础URL
const getBaseURL = () => {
  // 如果有环境变量配置的API地址，优先使用
  if (process.env.VUE_APP_API_BASE_URL) {
    return process.env.VUE_APP_API_BASE_URL
  }

  // 开发环境
  if (process.env.NODE_ENV === 'development') {
    return 'http://localhost:8082/api'
  }

  // 生产环境 - 使用相对路径，通过Nginx代理
  return '/api'
}

// 创建axios实例
const api = axios.create({
  baseURL: getBaseURL(),
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('库存API请求:', config.method?.toUpperCase(), config.url, config.data || config.params)
    return config
  },
  error => {
    console.error('库存API请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('库存API响应:', response.config.url, response.data)
    return response
  },
  error => {
    console.error('库存API响应错误:', error.response?.data || error.message)
    return Promise.reject(error)
  }
)

// 库存管理API
export const stockAPI = {
  /**
   * 获取所有库存信息
   */
  getAllStock() {
    return api.get('/stock/list')
  },

  /**
   * 获取库存不足的商品
   */
  getLowStockProducts() {
    return api.get('/stock/low-stock')
  },

  /**
   * 根据商品ID获取库存
   * @param {number} productId - 商品ID
   */
  getStockByProductId(productId) {
    return api.get(`/stock/product/${productId}`)
  },

  /**
   * 更新库存数量
   * @param {Object} stockData - 库存数据
   * @param {number} stockData.productId - 商品ID
   * @param {number} stockData.quantity - 库存数量
   */
  updateStock(stockData) {
    return api.put('/stock/update', stockData)
  },

  /**
   * 增加库存（入库）
   * @param {Object} stockData - 库存数据
   * @param {number} stockData.productId - 商品ID
   * @param {number} stockData.amount - 增加数量
   */
  increaseStock(stockData) {
    return api.post('/stock/increase', stockData)
  },

  /**
   * 减少库存（出库）
   * @param {Object} stockData - 库存数据
   * @param {number} stockData.productId - 商品ID
   * @param {number} stockData.amount - 减少数量
   */
  reduceStock(stockData) {
    return api.post('/stock/reduce', stockData)
  },

  /**
   * 库存调整（综合方法）
   * @param {Object} adjustmentData - 调整数据
   * @param {number} adjustmentData.productId - 商品ID
   * @param {string} adjustmentData.type - 调整类型：'add', 'reduce', 'set'
   * @param {number} adjustmentData.quantity - 调整数量
   * @param {string} adjustmentData.reason - 调整原因
   */
  async adjustStock(adjustmentData) {
    const { productId, type, quantity } = adjustmentData

    switch (type) {
      case 'add':
        return this.increaseStock({ productId, amount: quantity })
      case 'reduce':
        return this.reduceStock({ productId, amount: quantity })
      case 'set':
        return this.updateStock({ productId, quantity })
      default:
        throw new Error('不支持的调整类型')
    }
  }
}

export default stockAPI
