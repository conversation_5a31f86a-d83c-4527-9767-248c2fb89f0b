/**
 * 环境配置文件
 * 统一管理不同环境下的配置信息
 */

// 获取当前环境
const ENV = process.env.NODE_ENV || 'development'

// 环境配置
const config = {
  development: {
    // 开发环境配置
    API_BASE_URL: 'http://localhost:8082/api',
    BASE_URL: 'http://localhost:8083',
    IMAGE_BASE_URL: 'http://localhost:8082'
  },
  production: {
    // 生产环境配置
    API_BASE_URL: '/api',  // 通过Nginx代理
    BASE_URL: 'http://*************',
    IMAGE_BASE_URL: ''  // 生产环境使用相对路径，通过Nginx代理
  }
}

// 导出当前环境的配置
export default config[ENV]

// 导出具体的配置项
export const API_BASE_URL = config[ENV].API_BASE_URL
export const BASE_URL = config[ENV].BASE_URL
export const IMAGE_BASE_URL = config[ENV].IMAGE_BASE_URL

// 获取图片完整URL的工具函数
export const getImageUrl = (imageUrl) => {
  if (!imageUrl || imageUrl.trim() === '') return ''
  
  // 如果已经是完整URL，直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }
  
  // 如果是相对路径，添加服务器地址
  return `${IMAGE_BASE_URL}${imageUrl}`
}

// 获取API完整URL的工具函数
export const getApiUrl = (path) => {
  return `${API_BASE_URL}${path}`
}
