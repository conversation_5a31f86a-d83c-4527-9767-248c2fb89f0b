#!/bin/bash

# 修复CORS跨域问题的完整解决方案
# 适用于前后端分离部署的情况

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}        CORS跨域问题修复方案           ${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 云服务器IP
SERVER_IP="*************"

echo -e "${YELLOW}问题分析:${NC}"
echo "前端部署在: http://$SERVER_IP"
echo "后端运行在: http://localhost:8082"
echo "问题: 前端尝试访问localhost:8082导致CORS错误"
echo

echo -e "${YELLOW}解决方案:${NC}"
echo "1. 修复前端API配置，使用相对路径"
echo "2. 修复后端CORS配置，添加云服务器IP"
echo "3. 确保Nginx代理配置正确"
echo "4. 重新构建和部署"
echo

# 1. 检查前端配置
echo -e "${YELLOW}1. 检查前端API配置...${NC}"

if [ -f "vue-product/src/api/user.js" ]; then
    echo "检查 user.js API配置..."
    if grep -q "localhost:8082" vue-product/src/api/user.js; then
        echo -e "${RED}❌ 发现localhost:8082硬编码${NC}"
        echo "需要确保生产环境使用相对路径"
    else
        echo -e "${GREEN}✅ user.js API配置正确${NC}"
    fi
else
    echo -e "${RED}❌ vue-product/src/api/user.js 文件不存在${NC}"
fi

# 2. 检查后端CORS配置
echo -e "${YELLOW}2. 检查后端CORS配置...${NC}"

if [ -f "product-management-system/src/main/java/qidian/it/springboot/config/WebConfig.java" ]; then
    echo "检查 WebConfig.java CORS配置..."
    if grep -q "$SERVER_IP" product-management-system/src/main/java/qidian/it/springboot/config/WebConfig.java; then
        echo -e "${GREEN}✅ CORS配置包含云服务器IP${NC}"
    else
        echo -e "${RED}❌ CORS配置缺少云服务器IP${NC}"
        echo "需要在allowedOrigins中添加: http://$SERVER_IP"
    fi
else
    echo -e "${RED}❌ WebConfig.java 文件不存在${NC}"
fi

# 3. 检查Nginx配置
echo -e "${YELLOW}3. 检查Nginx代理配置...${NC}"

if [ -f "nginx-pms.conf" ]; then
    echo "检查 nginx-pms.conf 配置..."
    if grep -q "location /api/" nginx-pms.conf; then
        echo -e "${GREEN}✅ Nginx API代理配置存在${NC}"
        if grep -q "proxy_pass http://localhost:8082/api/" nginx-pms.conf; then
            echo -e "${GREEN}✅ 代理目标地址正确${NC}"
        else
            echo -e "${RED}❌ 代理目标地址不正确${NC}"
        fi
    else
        echo -e "${RED}❌ 缺少API代理配置${NC}"
    fi
else
    echo -e "${RED}❌ nginx-pms.conf 文件不存在${NC}"
fi

echo

# 4. 提供修复建议
echo -e "${YELLOW}4. 修复建议:${NC}"
echo

echo -e "${BLUE}前端修复:${NC}"
echo "确保所有API文件使用环境变量配置:"
echo "  开发环境: http://localhost:8082/api"
echo "  生产环境: /api (相对路径)"
echo

echo -e "${BLUE}后端修复:${NC}"
echo "在WebConfig.java的allowedOrigins中添加:"
echo "  \"http://$SERVER_IP\","
echo "  \"http://$SERVER_IP:80\","
echo "  \"http://$SERVER_IP:8080\","
echo "  \"http://$SERVER_IP:8083\""
echo

echo -e "${BLUE}Nginx配置:${NC}"
echo "确保nginx-pms.conf包含:"
echo "  location /api/ {"
echo "      proxy_pass http://localhost:8082/api/;"
echo "      proxy_set_header Host \$host;"
echo "      proxy_set_header X-Real-IP \$remote_addr;"
echo "      proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;"
echo "      proxy_set_header X-Forwarded-Proto \$scheme;"
echo "  }"
echo

# 5. 部署步骤
echo -e "${YELLOW}5. 部署步骤:${NC}"
echo

echo "步骤1: 重新构建前端"
echo "  cd vue-product"
echo "  npm run build"
echo

echo "步骤2: 重新编译后端"
echo "  cd product-management-system"
echo "  mvn clean package -DskipTests"
echo

echo "步骤3: 部署到云服务器"
echo "  # 上传前端文件到 /opt/pms/frontend/"
echo "  # 上传后端jar到 /opt/pms/product-management-system/"
echo "  # 更新nginx配置"
echo

echo "步骤4: 重启服务"
echo "  sudo systemctl restart pms-backend"
echo "  sudo systemctl restart nginx"
echo

echo "步骤5: 测试"
echo "  curl http://$SERVER_IP/api/health"
echo "  # 在浏览器中访问 http://$SERVER_IP"
echo

# 6. 故障排除
echo -e "${YELLOW}6. 故障排除:${NC}"
echo

echo "如果仍有CORS问题:"
echo "1. 检查浏览器开发者工具的Network标签"
echo "2. 确认请求URL是否正确"
echo "3. 检查后端日志是否有错误"
echo "4. 验证Nginx是否正确代理请求"
echo

echo "常用调试命令:"
echo "  # 检查后端服务状态"
echo "  sudo systemctl status pms-backend"
echo "  # 检查Nginx状态"
echo "  sudo systemctl status nginx"
echo "  # 查看后端日志"
echo "  tail -f /opt/pms/product-management-system/logs/app.log"
echo "  # 测试API连接"
echo "  curl -v http://localhost:8082/api/health"
echo "  curl -v http://$SERVER_IP/api/health"
echo

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}        修复方案完成                    ${NC}"
echo -e "${BLUE}========================================${NC}"
