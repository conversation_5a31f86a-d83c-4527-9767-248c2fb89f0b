<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员统计数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
        }
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员后台统计数据测试</h1>
        <p>这个页面用于测试管理员后台统计数据API是否正常工作。</p>
        
        <button onclick="loadStats()">加载统计数据</button>
        
        <div id="status"></div>
        
        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stat-card">
                <div class="stat-label">商品总数</div>
                <div class="stat-number" id="totalProducts">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">分类总数</div>
                <div class="stat-number" id="totalCategories">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">订单总数</div>
                <div class="stat-number" id="totalOrders">0</div>
            </div>
            <div class="stat-card">
                <div class="stat-label">用户总数</div>
                <div class="stat-number" id="totalUsers">0</div>
            </div>
        </div>
        
        <div id="rawData" style="margin-top: 20px;"></div>
    </div>

    <script>
        async function loadStats() {
            const statusDiv = document.getElementById('status');
            const statsGrid = document.getElementById('statsGrid');
            const rawDataDiv = document.getElementById('rawData');
            
            statusDiv.innerHTML = '<div class="loading">正在加载统计数据...</div>';
            statsGrid.style.display = 'none';
            rawDataDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://*************:8082/api/admin/dashboard/statistics');
                const data = await response.json();
                
                console.log('API响应:', data);
                
                if (data.success) {
                    // 更新统计卡片
                    document.getElementById('totalProducts').textContent = data.data.totalProducts || 0;
                    document.getElementById('totalCategories').textContent = data.data.totalCategories || 0;
                    document.getElementById('totalOrders').textContent = data.data.totalOrders || 0;
                    document.getElementById('totalUsers').textContent = data.data.totalUsers || 0;
                    
                    // 显示成功消息
                    statusDiv.innerHTML = '<div class="success">统计数据加载成功！</div>';
                    statsGrid.style.display = 'grid';
                    
                    // 显示原始数据
                    rawDataDiv.innerHTML = `
                        <h3>原始API响应数据：</h3>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `<div class="error">加载失败：${data.message}</div>`;
                }
            } catch (error) {
                console.error('请求失败:', error);
                statusDiv.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            }
        }
        
        // 页面加载时自动加载一次数据
        window.onload = function() {
            loadStats();
        };
    </script>
</body>
</html>
