#!/usr/bin/env node

/**
 * 清理项目中的console语句
 * 用于生产环境构建前的代码清理
 */

const fs = require('fs');
const path = require('path');

// 需要处理的文件扩展名
const FILE_EXTENSIONS = ['.js', '.vue', '.ts'];

// 需要扫描的目录
const SCAN_DIRS = ['src'];

// console语句的正则表达式
const CONSOLE_PATTERNS = [
  /console\.(log|info|warn|error|debug|trace)\([^)]*\);?\s*\n?/g,
  /console\.(log|info|warn|error|debug|trace)\([^)]*\);?\s*$/gm
];

/**
 * 检查文件是否需要处理
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath);
  return FILE_EXTENSIONS.includes(ext);
}

/**
 * 获取所有需要处理的文件
 */
function getAllFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      // 跳过node_modules和dist目录
      if (!['node_modules', 'dist', '.git'].includes(file)) {
        getAllFiles(filePath, fileList);
      }
    } else if (shouldProcessFile(filePath)) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

/**
 * 清理文件中的console语句
 */
function cleanConsoleInFile(filePath) {
  try {
    let content = fs.readFileSync(filePath, 'utf8');
    let originalContent = content;
    let removedCount = 0;
    
    // 应用所有console清理模式
    CONSOLE_PATTERNS.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        removedCount += matches.length;
        content = content.replace(pattern, '');
      }
    });
    
    // 清理多余的空行
    content = content.replace(/\n\s*\n\s*\n/g, '\n\n');
    
    // 如果内容有变化，写回文件
    if (content !== originalContent) {
      fs.writeFileSync(filePath, content, 'utf8');
      console.log(`✅ ${filePath}: 移除了 ${removedCount} 个console语句`);
      return removedCount;
    }
    
    return 0;
  } catch (error) {
    console.error(`❌ 处理文件失败 ${filePath}:`, error.message);
    return 0;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🧹 开始清理console语句...\n');
  
  let totalFiles = 0;
  let processedFiles = 0;
  let totalRemoved = 0;
  
  // 扫描所有指定目录
  SCAN_DIRS.forEach(dir => {
    if (fs.existsSync(dir)) {
      const files = getAllFiles(dir);
      totalFiles += files.length;
      
      files.forEach(file => {
        const removed = cleanConsoleInFile(file);
        if (removed > 0) {
          processedFiles++;
          totalRemoved += removed;
        }
      });
    } else {
      console.warn(`⚠️  目录不存在: ${dir}`);
    }
  });
  
  console.log('\n📊 清理统计:');
  console.log(`   扫描文件: ${totalFiles} 个`);
  console.log(`   处理文件: ${processedFiles} 个`);
  console.log(`   移除console语句: ${totalRemoved} 个`);
  
  if (totalRemoved > 0) {
    console.log('\n✨ console语句清理完成！');
  } else {
    console.log('\n✅ 没有发现需要清理的console语句');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { cleanConsoleInFile, getAllFiles };
