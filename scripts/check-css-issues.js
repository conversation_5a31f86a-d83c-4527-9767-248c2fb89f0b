#!/usr/bin/env node

/**
 * 检查Vue文件中的CSS语法问题
 * 特别是mask属性和其他可能导致构建错误的CSS问题
 */

const fs = require('fs');
const path = require('path');

// 需要检查的CSS问题模式
const CSS_ISSUES = [
  {
    name: 'Mask属性语法错误',
    pattern: /mask\s*:\s*linear-gradient\([^)]+\)\s+content-box\s*,\s*linear-gradient\([^)]+\)/g,
    description: '复杂的mask属性语法可能导致浏览器兼容性问题'
  },
  {
    name: 'Webkit-mask属性语法错误',
    pattern: /-webkit-mask\s*:\s*linear-gradient\([^)]+\)\s+content-box\s*,\s*linear-gradient\([^)]+\)/g,
    description: '复杂的-webkit-mask属性语法可能导致浏览器兼容性问题'
  },
  {
    name: 'Mask-composite语法问题',
    pattern: /mask-composite\s*:\s*(xor|exclude)/g,
    description: 'mask-composite属性兼容性有限'
  },
  {
    name: 'Background-clip文本裁剪',
    pattern: /-webkit-background-clip\s*:\s*text/g,
    description: '检查background-clip: text的兼容性'
  },
  {
    name: '无效的CSS函数',
    pattern: /url\(\s*[^)]*\s*\)\s*[^;,}]+/g,
    description: '检查URL函数的语法'
  }
];

/**
 * 扫描目录获取所有Vue文件
 */
function getAllVueFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      if (!['node_modules', 'dist', '.git'].includes(file)) {
        getAllVueFiles(filePath, fileList);
      }
    } else if (file.endsWith('.vue')) {
      fileList.push(filePath);
    }
  });
  
  return fileList;
}

/**
 * 检查文件中的CSS问题
 */
function checkCSSIssues(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const issues = [];
    
    // 提取style标签内容
    const styleMatches = content.match(/<style[^>]*>([\s\S]*?)<\/style>/g);
    if (!styleMatches) return issues;
    
    styleMatches.forEach((styleBlock, blockIndex) => {
      const styleContent = styleBlock.replace(/<\/?style[^>]*>/g, '');
      
      CSS_ISSUES.forEach(issue => {
        const matches = styleContent.match(issue.pattern);
        if (matches) {
          matches.forEach(match => {
            // 计算行号
            const beforeMatch = content.substring(0, content.indexOf(match));
            const lineNumber = beforeMatch.split('\n').length;
            
            issues.push({
              file: filePath,
              line: lineNumber,
              issue: issue.name,
              description: issue.description,
              code: match.trim(),
              blockIndex: blockIndex + 1
            });
          });
        }
      });
    });
    
    return issues;
  } catch (error) {
    console.error(`❌ 检查文件失败 ${filePath}:`, error.message);
    return [];
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔍 开始检查CSS语法问题...\n');
  
  const srcDir = 'src';
  if (!fs.existsSync(srcDir)) {
    console.error('❌ src目录不存在');
    return;
  }
  
  const vueFiles = getAllVueFiles(srcDir);
  let totalIssues = 0;
  let filesWithIssues = 0;
  
  console.log(`📁 扫描到 ${vueFiles.length} 个Vue文件\n`);
  
  vueFiles.forEach(file => {
    const issues = checkCSSIssues(file);
    if (issues.length > 0) {
      filesWithIssues++;
      totalIssues += issues.length;
      
      console.log(`📄 ${file}:`);
      issues.forEach(issue => {
        console.log(`   ⚠️  第${issue.line}行: ${issue.issue}`);
        console.log(`      ${issue.description}`);
        console.log(`      代码: ${issue.code}`);
        console.log('');
      });
    }
  });
  
  console.log('📊 检查结果:');
  console.log(`   扫描文件: ${vueFiles.length} 个`);
  console.log(`   问题文件: ${filesWithIssues} 个`);
  console.log(`   总问题数: ${totalIssues} 个`);
  
  if (totalIssues === 0) {
    console.log('\n✅ 没有发现CSS语法问题！');
  } else {
    console.log('\n🔧 建议修复上述CSS问题以避免构建错误');
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { checkCSSIssues, getAllVueFiles };
