#!/bin/bash

# 部署验证脚本
# 用于验证商品管理系统在云主机上的部署状态

echo "=========================================="
echo "商品管理系统部署验证"
echo "云主机IP: *************"
echo "=========================================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查后端服务
check_backend() {
    echo -e "\n${YELLOW}检查后端服务...${NC}"
    
    # 检查后端API健康状态
    echo "检查后端API健康状态..."
    if curl -s -f "http://*************:8082/api/health" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 后端API服务正常${NC}"
    else
        echo -e "${RED}❌ 后端API服务异常${NC}"
    fi
    
    # 检查数据库连接
    echo "检查数据库连接..."
    if curl -s -f "http://*************:8082/api/categories" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 数据库连接正常${NC}"
    else
        echo -e "${RED}❌ 数据库连接异常${NC}"
    fi
}

# 检查前端服务
check_frontend() {
    echo -e "\n${YELLOW}检查前端服务...${NC}"
    
    # 检查前端页面
    echo "检查前端页面..."
    if curl -s -f "http://*************/" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 前端页面正常${NC}"
    else
        echo -e "${RED}❌ 前端页面异常${NC}"
    fi
    
    # 检查静态资源
    echo "检查静态资源..."
    if curl -s -f "http://*************/static/js/" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 静态资源正常${NC}"
    else
        echo -e "${RED}❌ 静态资源异常${NC}"
    fi
}

# 检查Nginx代理
check_nginx() {
    echo -e "\n${YELLOW}检查Nginx代理...${NC}"
    
    # 检查API代理
    echo "检查API代理..."
    if curl -s -f "http://*************/api/categories" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ API代理正常${NC}"
    else
        echo -e "${RED}❌ API代理异常${NC}"
    fi
    
    # 检查图片代理
    echo "检查图片代理..."
    if curl -s -I "http://*************/product_image/" | grep -q "200\|404"; then
        echo -e "${GREEN}✅ 图片代理正常${NC}"
    else
        echo -e "${RED}❌ 图片代理异常${NC}"
    fi
}

# 检查关键功能
check_functions() {
    echo -e "\n${YELLOW}检查关键功能...${NC}"
    
    # 检查商品列表
    echo "检查商品列表API..."
    if curl -s -f "http://*************:8082/api/products" > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 商品列表API正常${NC}"
    else
        echo -e "${RED}❌ 商品列表API异常${NC}"
    fi
    
    # 检查用户注册API
    echo "检查用户注册API..."
    if curl -s -f "http://*************:8082/api/users/register" -X POST -H "Content-Type: application/json" -d '{}' > /dev/null 2>&1; then
        echo -e "${GREEN}✅ 用户注册API正常${NC}"
    else
        echo -e "${RED}❌ 用户注册API异常${NC}"
    fi
}

# 检查端口占用
check_ports() {
    echo -e "\n${YELLOW}检查端口占用...${NC}"
    
    # 检查8082端口（后端）
    if netstat -tuln | grep -q ":8082"; then
        echo -e "${GREEN}✅ 端口8082正在监听（后端服务）${NC}"
    else
        echo -e "${RED}❌ 端口8082未监听${NC}"
    fi
    
    # 检查80端口（Nginx）
    if netstat -tuln | grep -q ":80"; then
        echo -e "${GREEN}✅ 端口80正在监听（Nginx服务）${NC}"
    else
        echo -e "${RED}❌ 端口80未监听${NC}"
    fi
}

# 检查服务状态
check_services() {
    echo -e "\n${YELLOW}检查系统服务状态...${NC}"
    
    # 检查后端服务
    if systemctl is-active --quiet pms-backend; then
        echo -e "${GREEN}✅ pms-backend服务运行中${NC}"
    else
        echo -e "${RED}❌ pms-backend服务未运行${NC}"
    fi
    
    # 检查Nginx服务
    if systemctl is-active --quiet nginx; then
        echo -e "${GREEN}✅ nginx服务运行中${NC}"
    else
        echo -e "${RED}❌ nginx服务未运行${NC}"
    fi
    
    # 检查MySQL服务
    if systemctl is-active --quiet mysqld || systemctl is-active --quiet mysql; then
        echo -e "${GREEN}✅ MySQL服务运行中${NC}"
    else
        echo -e "${RED}❌ MySQL服务未运行${NC}"
    fi
}

# 显示访问地址
show_access_info() {
    echo -e "\n${YELLOW}访问地址信息:${NC}"
    echo "前端页面: http://*************"
    echo "后端API: http://*************:8082"
    echo "API文档: http://*************:8082/swagger-ui.html"
    echo "管理后台: http://*************/admin"
    echo ""
    echo "测试页面:"
    echo "- 管理员统计: http://*************/test-admin-stats.html"
    echo "- 订单统计: http://*************/test-order-stats.html"
    echo "- 前端API测试: http://*************/test-frontend-api.html"
}

# 主函数
main() {
    case "$1" in
        "backend")
            check_backend
            ;;
        "frontend")
            check_frontend
            ;;
        "nginx")
            check_nginx
            ;;
        "functions")
            check_functions
            ;;
        "ports")
            check_ports
            ;;
        "services")
            check_services
            ;;
        "all"|"")
            check_services
            check_ports
            check_backend
            check_frontend
            check_nginx
            check_functions
            show_access_info
            ;;
        *)
            echo "用法: $0 {backend|frontend|nginx|functions|ports|services|all}"
            echo "  backend   - 检查后端服务"
            echo "  frontend  - 检查前端服务"
            echo "  nginx     - 检查Nginx代理"
            echo "  functions - 检查关键功能"
            echo "  ports     - 检查端口占用"
            echo "  services  - 检查系统服务"
            echo "  all       - 检查所有项目（默认）"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
