#!/bin/bash

# 修复localhost:8082引用的脚本
# 用于检查和修复项目中的硬编码localhost:8082引用

echo "=========================================="
echo "检查和修复localhost:8082引用"
echo "=========================================="

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_localhost_references() {
    echo -e "${YELLOW}正在检查localhost:8082引用...${NC}"
    
    # 在前端代码中查找
    echo -e "\n${YELLOW}前端代码中的引用:${NC}"
    find vue-product/src -name "*.js" -o -name "*.vue" | xargs grep -n "localhost:8082" 2>/dev/null || echo "未找到前端引用"
    
    # 在后端代码中查找
    echo -e "\n${YELLOW}后端代码中的引用:${NC}"
    find product-management-system/src -name "*.java" -o -name "*.yml" -o -name "*.properties" | xargs grep -n "localhost:8082" 2>/dev/null || echo "未找到后端引用"
    
    # 在配置文件中查找
    echo -e "\n${YELLOW}配置文件中的引用:${NC}"
    find . -name "*.conf" -o -name "*.sh" -o -name "*.md" | xargs grep -n "localhost:8082" 2>/dev/null || echo "未找到配置文件引用"
    
    # 在测试文件中查找
    echo -e "\n${YELLOW}测试文件中的引用:${NC}"
    find . -name "test-*.html" | xargs grep -n "localhost:8082" 2>/dev/null || echo "未找到测试文件引用"
}

# 修复函数
fix_references() {
    echo -e "\n${YELLOW}开始修复引用...${NC}"
    
    # 备份原文件
    echo "创建备份..."
    mkdir -p backup/$(date +%Y%m%d_%H%M%S)
    BACKUP_DIR="backup/$(date +%Y%m%d_%H%M%S)"
    
    # 修复测试文件中的引用（将localhost:8082改为云主机IP）
    echo "修复测试文件..."
    for file in test-*.html; do
        if [ -f "$file" ]; then
            cp "$file" "$BACKUP_DIR/"
            sed -i 's/localhost:8082/*************:8082/g' "$file"
            echo "已修复: $file"
        fi
    done
    
    echo -e "${GREEN}修复完成！${NC}"
    echo "备份文件保存在: $BACKUP_DIR"
}

# 验证修复结果
verify_fixes() {
    echo -e "\n${YELLOW}验证修复结果...${NC}"
    
    # 检查是否还有localhost:8082引用（排除备份目录和脚本本身）
    remaining=$(find . -name "*.js" -o -name "*.vue" -o -name "*.java" -o -name "*.yml" -o -name "*.html" -o -name "*.conf" -o -name "*.sh" -o -name "*.md" | grep -v backup | grep -v fix-localhost-references.sh | xargs grep -l "localhost:8082" 2>/dev/null | wc -l)
    
    if [ $remaining -eq 0 ]; then
        echo -e "${GREEN}✅ 所有localhost:8082引用已修复！${NC}"
    else
        echo -e "${RED}⚠️  仍有 $remaining 个文件包含localhost:8082引用${NC}"
        echo "剩余引用:"
        find . -name "*.js" -o -name "*.vue" -o -name "*.java" -o -name "*.yml" -o -name "*.html" -o -name "*.conf" -o -name "*.sh" -o -name "*.md" | grep -v backup | grep -v fix-localhost-references.sh | xargs grep -n "localhost:8082" 2>/dev/null
    fi
}

# 显示修复建议
show_recommendations() {
    echo -e "\n${YELLOW}修复建议:${NC}"
    echo "1. 前端代码应使用环境变量或配置文件来管理API地址"
    echo "2. 后端代码应使用配置文件来管理回调地址"
    echo "3. 生产环境应通过Nginx代理来避免硬编码IP地址"
    echo "4. 测试文件可以使用云主机IP地址"
    echo ""
    echo "已修复的内容:"
    echo "✅ application.yml - 支付宝回调地址"
    echo "✅ AliPayController.java - 支付回调地址"
    echo "✅ Cart.vue - 图片URL处理"
    echo "✅ ProductDetail.vue - 图片URL处理"
    echo "✅ OrderManagement.vue - 图片URL处理"
    echo "✅ ProductManagement.vue - 图片URL处理"
    echo "✅ 创建了环境配置文件 src/config/env.js"
}

# 主函数
main() {
    case "$1" in
        "check")
            check_localhost_references
            ;;
        "fix")
            check_localhost_references
            fix_references
            verify_fixes
            show_recommendations
            ;;
        "verify")
            verify_fixes
            ;;
        *)
            echo "用法: $0 {check|fix|verify}"
            echo "  check  - 检查localhost:8082引用"
            echo "  fix    - 修复localhost:8082引用"
            echo "  verify - 验证修复结果"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
