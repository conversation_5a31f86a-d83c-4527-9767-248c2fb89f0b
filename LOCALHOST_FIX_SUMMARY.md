# localhost:8082 问题修复总结

## 问题描述

在云主机(*************)部署商品管理系统时，发现多个文件中硬编码了`localhost:8082`，导致生产环境功能异常。

## 修复内容

### 1. 后端修复

#### 1.1 配置文件修复
- **文件**: `product-management-system/src/main/resources/application.yml`
- **修复**: 支付宝回调地址从 `http://localhost:8082/api/alipay/notify` 改为 `http://*************:8082/api/alipay/notify`

#### 1.2 Java代码修复
- **文件**: `product-management-system/src/main/java/qidian/it/springboot/controller/AliPayController.java`
- **修复内容**:
  - 支付同步回调地址: `http://localhost:8082/api/alipay/return` → `http://*************:8082/api/alipay/return`
  - 支付成功跳转: `http://localhost:8083/orders` → `http://*************/orders`
  - 支付失败跳转: `http://localhost:8083/orders` → `http://*************/orders`

### 2. 前端修复

#### 2.1 图片URL处理
修复了以下文件中的图片URL硬编码问题：
- `vue-product/src/views/Cart.vue`
- `vue-product/src/views/ProductDetail.vue`
- `vue-product/src/components/OrderManagement.vue`
- `vue-product/src/components/ProductManagement.vue`

**修复方案**: 使用环境判断动态设置图片服务器地址
```javascript
const serverUrl = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8082'
  : ''  // 生产环境使用相对路径，通过Nginx代理
```

#### 2.2 支付相关API调用
修复了以下文件中的支付API硬编码问题：
- `vue-product/src/views/Cart.vue`
- `vue-product/src/views/OrderDetail.vue`
- `vue-product/src/views/Orders.vue`

**修复方案**: 使用环境判断动态设置API地址
```javascript
const baseUrl = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8082' 
  : 'http://*************:8082'
```

### 3. 测试文件修复

修复了测试文件中的API地址：
- `test-admin-stats.html`
- `test-order-stats.html`

将 `http://localhost:8082` 改为 `http://*************:8082`

### 4. 新增配置文件

#### 4.1 环境配置文件
- **文件**: `vue-product/src/config/env.js`
- **功能**: 统一管理不同环境的配置信息
- **内容**: 
  - API基础URL配置
  - 图片服务器配置
  - 工具函数

#### 4.2 修复脚本
- **文件**: `fix-localhost-references.sh`
- **功能**: 检查和修复localhost:8082引用

#### 4.3 验证脚本
- **文件**: `verify-deployment.sh`
- **功能**: 验证部署后的服务状态

## 修复后的架构

### 开发环境
- 前端: `http://localhost:8083`
- 后端: `http://localhost:8082`
- API调用: 直接调用 `http://localhost:8082/api`
- 图片访问: 直接访问 `http://localhost:8082/product_image`

### 生产环境
- 前端: `http://*************` (通过Nginx)
- 后端: `http://*************:8082`
- API调用: 通过Nginx代理 `/api` → `http://localhost:8082/api`
- 图片访问: 通过Nginx代理 `/product_image` → `http://localhost:8082/product_image`

## 部署步骤

### 1. 构建项目
```bash
# 构建后端
cd product-management-system
mvn clean package -DskipTests

# 构建前端
cd vue-product
npm install
npm run build
```

### 2. 部署到服务器
```bash
# 复制后端jar包
sudo cp product-management-system/target/product-management-system-1.0-SNAPSHOT.jar /opt/pms/

# 复制前端构建文件
sudo cp -r vue-product/dist/* /opt/pms/frontend/

# 启动服务
sudo systemctl start pms-backend
sudo systemctl start nginx
```

### 3. 验证部署
```bash
# 运行验证脚本
bash verify-deployment.sh
```

## 访问地址

- **前端页面**: http://*************
- **后端API**: http://*************:8082
- **API文档**: http://*************:8082/swagger-ui.html
- **管理后台**: http://*************/admin

## 注意事项

1. **环境变量**: 确保生产环境正确设置了 `NODE_ENV=production`
2. **Nginx配置**: 确保Nginx正确配置了API和图片的代理
3. **防火墙**: 确保开放了80和8082端口
4. **数据库**: 确保MySQL服务正常运行且数据库已初始化
5. **文件权限**: 确保上传目录有正确的读写权限

## 测试建议

1. 测试用户注册和登录功能
2. 测试商品浏览和搜索功能
3. 测试购物车和订单功能
4. 测试支付功能（支付宝沙箱）
5. 测试图片上传和显示功能
6. 测试管理员后台功能

## 故障排除

如果遇到问题，可以：
1. 检查服务状态: `systemctl status pms-backend nginx mysqld`
2. 查看日志: `tail -f /opt/pms/logs/application.log`
3. 检查端口: `netstat -tuln | grep -E "80|8082|3306"`
4. 运行验证脚本: `bash verify-deployment.sh`
