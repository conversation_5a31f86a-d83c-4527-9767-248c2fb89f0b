<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理统计数据测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border-left: 4px solid;
        }
        .stat-card.total { border-left-color: #667eea; }
        .stat-card.pending { border-left-color: #f093fb; }
        .stat-card.paid { border-left-color: #4facfe; }
        .stat-card.shipped { border-left-color: #43e97b; }
        .stat-card.completed { border-left-color: #38f9d7; }
        .stat-card.cancelled { border-left-color: #ff6b6b; }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin: 10px 0;
            color: #333;
        }
        .stat-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            color: #155724;
            background: #d4edda;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .comparison {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 30px;
        }
        .api-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>订单管理统计数据测试</h1>
        <p>这个页面用于测试订单管理页面的统计数据API是否正常工作。</p>
        
        <div>
            <button onclick="loadOrderStats()">加载订单统计数据</button>
            <button onclick="loadDashboardStats()">加载首页统计数据</button>
        </div>
        
        <div id="status"></div>
        
        <div class="comparison">
            <div class="api-section">
                <h3>订单管理统计 (OrderManagement)</h3>
                <div class="stats-grid" id="orderStatsGrid" style="display: none;">
                    <div class="stat-card total">
                        <div class="stat-label">订单总数</div>
                        <div class="stat-number" id="orderTotalOrders">0</div>
                    </div>
                    <div class="stat-card pending">
                        <div class="stat-label">待支付</div>
                        <div class="stat-number" id="orderPendingPayment">0</div>
                    </div>
                    <div class="stat-card paid">
                        <div class="stat-label">已支付</div>
                        <div class="stat-number" id="orderPaid">0</div>
                    </div>
                    <div class="stat-card shipped">
                        <div class="stat-label">已发货</div>
                        <div class="stat-number" id="orderShipped">0</div>
                    </div>
                    <div class="stat-card completed">
                        <div class="stat-label">已完成</div>
                        <div class="stat-number" id="orderCompleted">0</div>
                    </div>
                    <div class="stat-card cancelled">
                        <div class="stat-label">已取消</div>
                        <div class="stat-number" id="orderCancelled">0</div>
                    </div>
                </div>
                <div id="orderRawData"></div>
            </div>
            
            <div class="api-section">
                <h3>管理员首页统计 (AdminDashboard)</h3>
                <div class="stats-grid" id="dashboardStatsGrid" style="display: none;">
                    <div class="stat-card total">
                        <div class="stat-label">商品总数</div>
                        <div class="stat-number" id="dashboardTotalProducts">0</div>
                    </div>
                    <div class="stat-card pending">
                        <div class="stat-label">分类总数</div>
                        <div class="stat-number" id="dashboardTotalCategories">0</div>
                    </div>
                    <div class="stat-card paid">
                        <div class="stat-label">订单总数</div>
                        <div class="stat-number" id="dashboardTotalOrders">0</div>
                    </div>
                    <div class="stat-card shipped">
                        <div class="stat-label">用户总数</div>
                        <div class="stat-number" id="dashboardTotalUsers">0</div>
                    </div>
                </div>
                <div id="dashboardRawData"></div>
            </div>
        </div>
    </div>

    <script>
        async function loadOrderStats() {
            const statusDiv = document.getElementById('status');
            const statsGrid = document.getElementById('orderStatsGrid');
            const rawDataDiv = document.getElementById('orderRawData');
            
            statusDiv.innerHTML = '<div class="loading">正在加载订单统计数据...</div>';
            statsGrid.style.display = 'none';
            rawDataDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://*************:8082/api/orders/admin/statistics');
                const data = await response.json();
                
                console.log('订单统计API响应:', data);
                
                if (data.success) {
                    // 更新统计卡片
                    document.getElementById('orderTotalOrders').textContent = data.data.total_orders || 0;
                    document.getElementById('orderPendingPayment').textContent = data.data.pending_payment || 0;
                    document.getElementById('orderPaid').textContent = data.data.paid || 0;
                    document.getElementById('orderShipped').textContent = data.data.shipped || 0;
                    document.getElementById('orderCompleted').textContent = data.data.completed || 0;
                    document.getElementById('orderCancelled').textContent = data.data.cancelled || 0;
                    
                    // 显示成功消息
                    statusDiv.innerHTML = '<div class="success">订单统计数据加载成功！</div>';
                    statsGrid.style.display = 'grid';
                    
                    // 显示原始数据
                    rawDataDiv.innerHTML = `
                        <h4>原始API响应数据：</h4>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `<div class="error">加载失败：${data.message}</div>`;
                }
            } catch (error) {
                console.error('请求失败:', error);
                statusDiv.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            }
        }
        
        async function loadDashboardStats() {
            const statusDiv = document.getElementById('status');
            const statsGrid = document.getElementById('dashboardStatsGrid');
            const rawDataDiv = document.getElementById('dashboardRawData');
            
            statusDiv.innerHTML = '<div class="loading">正在加载首页统计数据...</div>';
            statsGrid.style.display = 'none';
            rawDataDiv.innerHTML = '';
            
            try {
                const response = await fetch('http://*************:8082/api/admin/dashboard/statistics');
                const data = await response.json();
                
                console.log('首页统计API响应:', data);
                
                if (data.success) {
                    // 更新统计卡片
                    document.getElementById('dashboardTotalProducts').textContent = data.data.totalProducts || 0;
                    document.getElementById('dashboardTotalCategories').textContent = data.data.totalCategories || 0;
                    document.getElementById('dashboardTotalOrders').textContent = data.data.totalOrders || 0;
                    document.getElementById('dashboardTotalUsers').textContent = data.data.totalUsers || 0;
                    
                    // 显示成功消息
                    statusDiv.innerHTML = '<div class="success">首页统计数据加载成功！</div>';
                    statsGrid.style.display = 'grid';
                    
                    // 显示原始数据
                    rawDataDiv.innerHTML = `
                        <h4>原始API响应数据：</h4>
                        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px;">${JSON.stringify(data, null, 2)}</pre>
                    `;
                } else {
                    statusDiv.innerHTML = `<div class="error">加载失败：${data.message}</div>`;
                }
            } catch (error) {
                console.error('请求失败:', error);
                statusDiv.innerHTML = `<div class="error">请求失败：${error.message}</div>`;
            }
        }
        
        // 页面加载时自动加载订单统计数据
        window.onload = function() {
            loadOrderStats();
        };
    </script>
</body>
</html>
