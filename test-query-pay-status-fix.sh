#!/bin/bash

# 测试查询支付状态功能的localhost:8082修复情况
# 验证Orders.vue中的查询支付状态功能是否已正确修复

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    查询支付状态功能修复验证报告      ${NC}"
echo -e "${BLUE}========================================${NC}"
echo

# 1. 检查Orders.vue中的查询支付状态功能
echo -e "${YELLOW}1. 检查Orders.vue中的查询支付状态功能...${NC}"
echo

if [ -f "vue-product/src/views/Orders.vue" ]; then
    # 检查是否还有硬编码的localhost:8082（不包含环境变量判断的）
    hardcoded_count=$(grep -n "localhost:8082" vue-product/src/views/Orders.vue | grep -v "process.env.NODE_ENV" | wc -l)
    
    if [ $hardcoded_count -eq 0 ]; then
        echo -e "${GREEN}✅ Orders.vue: 无硬编码localhost:8082引用${NC}"
    else
        echo -e "${RED}❌ Orders.vue: 仍有 $hardcoded_count 个硬编码引用${NC}"
        grep -n "localhost:8082" vue-product/src/views/Orders.vue | grep -v "process.env.NODE_ENV"
    fi
    
    # 检查查询支付状态函数是否使用环境变量
    if grep -A 10 "queryPayStatus" vue-product/src/views/Orders.vue | grep -q "process.env.NODE_ENV"; then
        echo -e "${GREEN}✅ queryPayStatus函数: 已使用环境变量配置${NC}"
    else
        echo -e "${RED}❌ queryPayStatus函数: 未使用环境变量配置${NC}"
    fi
else
    echo -e "${RED}❌ Orders.vue文件不存在${NC}"
fi

echo

# 2. 检查OrderDetail.vue中的相关功能
echo -e "${YELLOW}2. 检查OrderDetail.vue中的相关功能...${NC}"
echo

if [ -f "vue-product/src/views/OrderDetail.vue" ]; then
    hardcoded_count=$(grep -n "localhost:8082" vue-product/src/views/OrderDetail.vue | grep -v "process.env.NODE_ENV" | wc -l)
    
    if [ $hardcoded_count -eq 0 ]; then
        echo -e "${GREEN}✅ OrderDetail.vue: 无硬编码localhost:8082引用${NC}"
    else
        echo -e "${RED}❌ OrderDetail.vue: 仍有 $hardcoded_count 个硬编码引用${NC}"
        grep -n "localhost:8082" vue-product/src/views/OrderDetail.vue | grep -v "process.env.NODE_ENV"
    fi
else
    echo -e "${RED}❌ OrderDetail.vue文件不存在${NC}"
fi

echo

# 3. 检查后端查询支付状态接口
echo -e "${YELLOW}3. 检查后端查询支付状态接口...${NC}"
echo

if [ -f "product-management-system/src/main/java/qidian/it/springboot/controller/AliPayController.java" ]; then
    if grep -q "query-pay-status" product-management-system/src/main/java/qidian/it/springboot/controller/AliPayController.java; then
        echo -e "${GREEN}✅ 后端接口: /api/alipay/query-pay-status 存在${NC}"
        
        # 检查是否有localhost:8082硬编码
        backend_hardcoded=$(grep -n "localhost:8082" product-management-system/src/main/java/qidian/it/springboot/controller/AliPayController.java | wc -l)
        if [ $backend_hardcoded -eq 0 ]; then
            echo -e "${GREEN}✅ 后端接口: 无localhost:8082硬编码${NC}"
        else
            echo -e "${RED}❌ 后端接口: 发现localhost:8082硬编码${NC}"
        fi
    else
        echo -e "${RED}❌ 后端接口: /api/alipay/query-pay-status 不存在${NC}"
    fi
else
    echo -e "${RED}❌ AliPayController.java文件不存在${NC}"
fi

echo

# 4. 检查Nginx配置是否支持代理
echo -e "${YELLOW}4. 检查Nginx配置...${NC}"
echo

if [ -f "nginx-pms.conf" ]; then
    if grep -q "/api/alipay" nginx-pms.conf; then
        echo -e "${GREEN}✅ Nginx配置: 支持/api/alipay路径代理${NC}"
    else
        echo -e "${YELLOW}⚠️  Nginx配置: 可能需要添加/api/alipay路径代理${NC}"
    fi
else
    echo -e "${YELLOW}⚠️  nginx-pms.conf文件不存在${NC}"
fi

echo

# 5. 总结报告
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}           修复总结                     ${NC}"
echo -e "${BLUE}========================================${NC}"

echo -e "${GREEN}✅ 已修复的问题:${NC}"
echo "1. Orders.vue中查询支付状态功能的localhost:8082硬编码"
echo "2. Orders.vue中其他支付相关功能的localhost:8082硬编码"
echo "3. OrderDetail.vue中支付功能的localhost:8082硬编码"
echo

echo -e "${BLUE}修复方案:${NC}"
echo "- 开发环境: 使用 http://localhost:8082"
echo "- 生产环境: 使用相对路径，通过Nginx代理到后端服务"
echo

echo -e "${BLUE}部署建议:${NC}"
echo "1. 重新构建前端项目: npm run build"
echo "2. 确保Nginx配置包含/api路径代理"
echo "3. 重启前端和后端服务"
echo "4. 测试查询支付状态功能"

echo
echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}           验证完成                     ${NC}"
echo -e "${BLUE}========================================${NC}"
